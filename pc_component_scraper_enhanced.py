#!/usr/bin/env python3
"""
Enhanced PC Component Price Scraper for Australian Hardware Stores

This script scrapes PC component prices from major Australian hardware retailers
for the specific components needed to build a PC:
- Video Cards (Graphics Cards)
- ATX Motherboards  
- ATX Cases
- RAM (Memory)
- CPU (Processors)
- Cooling (Fans, AIO, Air Coolers)
- PSU (Power Supplies)
- M.2 Drives (NVMe SSDs)

The output is formatted for easy integration with MCP systems for PC build optimization.
"""

import asyncio
import argparse
import json
import sys
from pathlib import Path
from typing import List, Dict, Optional
from pc_scan import EcommerceScraper, scan_pc_components_from_site, scan_all_australian_stores


class PCComponentManager:
    """Manager class for PC component scraping operations"""
    
    def __init__(self):
        self.scraper = EcommerceScraper()
        self.australian_stores = [
            "umart", "scorptec", "mwave", "computeralliance", "pccasegear",
            "ple", "centrecom", "techfast", "buckpcs", "cobratech",
            "radiumpcs", "pcbyte", "precisioncomputers", "umklogix",
            "igamingcomputer", "jw", "cataclysm", "titaniumpc",
            "bpctech", "bybcomputers"
        ]
        
        self.pc_components = {
            "video_cards": "Graphics Cards (RTX, GTX, Radeon)",
            "atx_motherboards": "ATX Motherboards", 
            "atx_cases": "ATX PC Cases",
            "ram": "Memory (DDR4/DDR5 RAM)",
            "cpu": "Processors (Intel/AMD CPUs)",
            "cooling": "Cooling Solutions (Fans, AIO, Air Coolers)",
            "psu": "Power Supplies (PSUs)",
            "m2_drives": "M.2 NVMe SSDs"
        }

    async def scan_single_store(self, store_name: str, categories: List[str] = None, max_pages: int = 2) -> Dict:
        """Scan a single store for PC components"""
        if store_name not in self.australian_stores:
            print(f"Error: Store '{store_name}' not found in configured stores.")
            print(f"Available stores: {', '.join(self.australian_stores)}")
            return {}
        
        if categories is None:
            categories = list(self.pc_components.keys())
        
        print(f"Scanning {store_name} for PC components...")
        print(f"Categories: {', '.join(categories)}")
        
        results = await scan_pc_components_from_site(store_name, categories, max_pages)
        return results

    async def scan_multiple_stores(self, store_names: List[str], categories: List[str] = None, max_pages: int = 2) -> Dict:
        """Scan multiple specific stores"""
        if categories is None:
            categories = list(self.pc_components.keys())
        
        all_results = {}
        
        for i, store_name in enumerate(store_names, 1):
            print(f"\n[{i}/{len(store_names)}] Scanning {store_name}...")
            
            if store_name not in self.australian_stores:
                print(f"Warning: Store '{store_name}' not configured, skipping...")
                continue
            
            try:
                results = await scan_pc_components_from_site(store_name, categories, max_pages)
                all_results[store_name] = results
                
                # Add delay between stores
                if i < len(store_names):
                    await asyncio.sleep(3)
                    
            except Exception as e:
                print(f"Error scanning {store_name}: {str(e)}")
                all_results[store_name] = {}
        
        return all_results

    async def scan_all_stores(self, categories: List[str] = None, max_pages: int = 1) -> Dict:
        """Scan all Australian stores"""
        if categories is None:
            categories = list(self.pc_components.keys())
        
        print("Starting comprehensive scan of all Australian PC hardware stores...")
        results = await scan_all_australian_stores(categories, max_pages)
        return results

    def export_for_mcp(self, results: Dict, output_file: str = None) -> Path:
        """Export results in MCP-friendly format"""
        if output_file is None:
            timestamp = asyncio.get_event_loop().time()
            output_file = f"pc_components_for_mcp_{int(timestamp)}.json"
        
        output_path = Path(output_file)
        
        # Create MCP-optimized format
        mcp_data = {
            "metadata": {
                "scraper": "Australian PC Component Scraper",
                "purpose": "PC Build Price Optimization",
                "timestamp": asyncio.get_event_loop().time(),
                "categories": list(self.pc_components.keys()),
                "stores_scanned": list(results.keys()) if isinstance(results, dict) else []
            },
            "components": {},
            "price_summary": {},
            "build_recommendations": {
                "budget_ranges": {
                    "budget": {"max_price": 1500, "description": "Budget gaming build"},
                    "mid_range": {"max_price": 3000, "description": "Mid-range gaming build"},
                    "high_end": {"max_price": 5000, "description": "High-end gaming build"},
                    "enthusiast": {"max_price": 10000, "description": "Enthusiast/workstation build"}
                }
            }
        }
        
        # Process results for MCP format
        all_products = []
        
        if isinstance(results, dict):
            for store_name, store_results in results.items():
                if isinstance(store_results, dict):
                    for category, category_results in store_results.items():
                        for result in category_results:
                            for product in result.products:
                                product_dict = product.dict()
                                product_dict["store"] = store_name
                                product_dict["category"] = category
                                product_dict["price_numeric"] = self.scraper.extract_price_value(product.price or "")
                                all_products.append(product_dict)
        
        # Organize by category for MCP
        for category in self.pc_components.keys():
            category_products = [p for p in all_products if p.get("category") == category]
            
            # Sort by price
            category_products.sort(key=lambda x: x.get("price_numeric", 0))
            
            mcp_data["components"][category] = {
                "description": self.pc_components[category],
                "total_products": len(category_products),
                "products": category_products,
                "price_range": {
                    "min": min([p.get("price_numeric", 0) for p in category_products]) if category_products else 0,
                    "max": max([p.get("price_numeric", 0) for p in category_products]) if category_products else 0,
                    "average": sum([p.get("price_numeric", 0) for p in category_products]) / len(category_products) if category_products else 0
                }
            }
        
        # Create price summary
        mcp_data["price_summary"] = {
            "total_products": len(all_products),
            "stores_with_data": len([s for s in results.keys() if any(results[s].values())]) if isinstance(results, dict) else 0,
            "categories_found": len([c for c in mcp_data["components"].keys() if mcp_data["components"][c]["total_products"] > 0])
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(mcp_data, f, indent=2, ensure_ascii=False)
        
        print(f"MCP-formatted data exported to: {output_path}")
        return output_path

    def print_summary(self, results: Dict):
        """Print a summary of scraping results"""
        print("\n" + "="*60)
        print("PC COMPONENT SCRAPING SUMMARY")
        print("="*60)
        
        if not results:
            print("No results to display.")
            return
        
        total_products = 0
        stores_with_data = 0
        
        for store_name, store_results in results.items():
            store_product_count = 0
            if isinstance(store_results, dict):
                for category_results in store_results.values():
                    store_product_count += sum(len(result.products) for result in category_results)
            
            if store_product_count > 0:
                stores_with_data += 1
                print(f"{store_name}: {store_product_count} products")
            
            total_products += store_product_count
        
        print(f"\nTotal products found: {total_products}")
        print(f"Stores with data: {stores_with_data}/{len(results)}")
        
        # Category breakdown
        print(f"\nProducts by category:")
        category_totals = {}
        
        for store_results in results.values():
            if isinstance(store_results, dict):
                for category, category_results in store_results.items():
                    category_count = sum(len(result.products) for result in category_results)
                    category_totals[category] = category_totals.get(category, 0) + category_count
        
        for category, count in category_totals.items():
            print(f"  {category}: {count} products")


async def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(description="Australian PC Component Price Scraper")
    parser.add_argument("--store", type=str, help="Scan specific store (e.g., 'umart')")
    parser.add_argument("--stores", nargs="+", help="Scan multiple specific stores")
    parser.add_argument("--all", action="store_true", help="Scan all Australian stores")
    parser.add_argument("--categories", nargs="+", 
                       choices=["video_cards", "atx_motherboards", "atx_cases", "ram", "cpu", "cooling", "psu", "m2_drives"],
                       help="Specific categories to scrape")
    parser.add_argument("--max-pages", type=int, default=2, help="Maximum pages to scrape per category")
    parser.add_argument("--output", type=str, help="Output file for MCP-formatted results")
    
    args = parser.parse_args()
    
    manager = PCComponentManager()
    
    print("=== AUSTRALIAN PC COMPONENT PRICE SCRAPER ===")
    print("Targeting major Australian PC hardware retailers")
    print()
    
    # Determine what to scrape
    if args.all:
        results = await manager.scan_all_stores(args.categories, args.max_pages)
    elif args.stores:
        results = await manager.scan_multiple_stores(args.stores, args.categories, args.max_pages)
    elif args.store:
        results = {args.store: await manager.scan_single_store(args.store, args.categories, args.max_pages)}
    else:
        # Default: demo with Umart
        print("No specific store specified. Running demo with Umart...")
        results = {"umart": await manager.scan_single_store("umart", args.categories, args.max_pages)}
    
    # Print summary
    manager.print_summary(results)
    
    # Export for MCP
    mcp_file = manager.export_for_mcp(results, args.output)
    
    print(f"\nResults ready for MCP integration: {mcp_file}")
    print("Use this data file with your MCP system for PC build optimization.")


if __name__ == "__main__":
    # Check for OpenAI API key
    import os
    if not os.getenv('OPENAI_API_KEY'):
        print("Warning: OPENAI_API_KEY environment variable not set.")
        print("Set it with: export OPENAI_API_KEY='your-api-key-here'")
        print("The scraper will still work but may have reduced extraction quality.")
        print()
    
    asyncio.run(main())
