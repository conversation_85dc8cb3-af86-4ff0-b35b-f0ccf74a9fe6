#!/usr/bin/env python3
"""
Prebuilt System Analyzer for PC Builder MCP

Analyzes pre-built systems from major manufacturers (HP, Dell, Lenovo, etc.)
and compares them to custom builds for value and performance.
"""

import asyncio
import logging
import re
from typing import Dict, List, Optional, Any, <PERSON><PERSON>
from dataclasses import dataclass
import aiohttp
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)

@dataclass
class PrebuiltSystem:
    """Represents a pre-built computer system"""
    name: str
    brand: str
    model: str
    price: float
    specifications: Dict[str, Any]
    url: str
    image_url: Optional[str]
    availability: str
    store: str
    use_case_fit: float
    performance_score: float
    value_score: float
    pros: List[str]
    cons: List[str]
    warranty_info: str
    is_clearance: bool

class PrebuiltAnalyzer:
    """Analyzes pre-built systems and compares to custom builds"""
    
    def __init__(self):
        # Australian retailers that sell pre-built systems
        self.prebuilt_sources = {
            "hp_australia": {
                "name": "HP Australia",
                "base_url": "https://www.hp.com/au-en",
                "search_paths": {
                    "gaming": "/shop/desktops/gaming-desktops",
                    "workstation": "/shop/desktops/workstations",
                    "office": "/shop/desktops/business-desktops",
                    "general": "/shop/desktops"
                }
            },
            "dell_australia": {
                "name": "Dell Australia", 
                "base_url": "https://www.dell.com/en-au",
                "search_paths": {
                    "gaming": "/shop/desktop-computers/alienware-gaming-desktops",
                    "workstation": "/shop/desktop-computers/precision-workstations",
                    "office": "/shop/desktop-computers/optiplex-business-desktops",
                    "general": "/shop/desktop-computers"
                }
            },
            "lenovo_australia": {
                "name": "Lenovo Australia",
                "base_url": "https://www.lenovo.com/au/en",
                "search_paths": {
                    "gaming": "/desktops-and-all-in-ones/legion-desktops",
                    "workstation": "/desktops-and-all-in-ones/thinkstation-workstations",
                    "office": "/desktops-and-all-in-ones/thinkcentre-business-desktops",
                    "general": "/desktops-and-all-in-ones"
                }
            },
            "umart_prebuilt": {
                "name": "Umart Pre-built",
                "base_url": "https://www.umart.com.au",
                "search_paths": {
                    "gaming": "/Gaming-PCs_C.html",
                    "workstation": "/Workstation-PCs_C.html",
                    "office": "/Office-PCs_C.html",
                    "general": "/Desktop-PCs_C.html"
                }
            },
            "scorptec_prebuilt": {
                "name": "Scorptec Pre-built",
                "base_url": "https://www.scorptec.com.au",
                "search_paths": {
                    "gaming": "/c/systems/gaming-pcs",
                    "workstation": "/c/systems/workstation-pcs",
                    "office": "/c/systems/office-pcs",
                    "general": "/c/systems"
                }
            },
            "pccasegear_prebuilt": {
                "name": "PC Case Gear Pre-built",
                "base_url": "https://www.pccasegear.com",
                "search_paths": {
                    "gaming": "/category/1411_1917/gaming-systems",
                    "workstation": "/category/1411_1918/workstation-systems",
                    "general": "/category/1411/systems"
                }
            }
        }
        
        # Performance benchmarks for common components (simplified)
        self.component_benchmarks = {
            "cpu": {
                "i9-13900K": 100,
                "i7-13700K": 85,
                "i5-13600K": 70,
                "i3-13100": 45,
                "Ryzen 9 7950X": 105,
                "Ryzen 7 7700X": 80,
                "Ryzen 5 7600X": 65,
                "Ryzen 5 5600X": 60,
            },
            "gpu": {
                "RTX 4090": 100,
                "RTX 4080": 80,
                "RTX 4070": 65,
                "RTX 4060": 45,
                "RTX 3080": 75,
                "RTX 3070": 60,
                "RTX 3060": 40,
                "RX 7900 XTX": 95,
                "RX 7800 XT": 70,
                "RX 6700 XT": 55,
            }
        }
    
    async def find_prebuilt_options(self, budget: float, use_case: str, 
                                  use_case_specs: Dict) -> List[PrebuiltSystem]:
        """Find pre-built systems that match the budget and use case"""
        all_systems = []
        
        # Search each source
        for source_key, source_config in self.prebuilt_sources.items():
            try:
                logger.info(f"Searching {source_config['name']} for pre-built systems...")
                systems = await self._search_prebuilt_source(
                    source_key, source_config, budget, use_case, use_case_specs
                )
                all_systems.extend(systems)
            except Exception as e:
                logger.error(f"Error searching {source_config['name']}: {str(e)}")
                continue
        
        # Filter and rank systems
        suitable_systems = self._filter_suitable_systems(all_systems, budget, use_case_specs)
        ranked_systems = self._rank_prebuilt_systems(suitable_systems, use_case, budget)
        
        return ranked_systems[:10]  # Return top 10 options
    
    async def _search_prebuilt_source(self, source_key: str, source_config: Dict,
                                    budget: float, use_case: str, 
                                    use_case_specs: Dict) -> List[PrebuiltSystem]:
        """Search a specific source for pre-built systems"""
        systems = []
        
        # For demonstration, we'll create mock data
        # In a real implementation, this would scrape actual websites
        
        if "hp" in source_key:
            systems.extend(self._generate_hp_systems(budget, use_case))
        elif "dell" in source_key:
            systems.extend(self._generate_dell_systems(budget, use_case))
        elif "lenovo" in source_key:
            systems.extend(self._generate_lenovo_systems(budget, use_case))
        elif "umart" in source_key:
            systems.extend(self._generate_australian_prebuilt_systems(source_config["name"], budget, use_case))
        elif "scorptec" in source_key:
            systems.extend(self._generate_australian_prebuilt_systems(source_config["name"], budget, use_case))
        elif "pccasegear" in source_key:
            systems.extend(self._generate_australian_prebuilt_systems(source_config["name"], budget, use_case))
        
        return systems
    
    def _generate_hp_systems(self, budget: float, use_case: str) -> List[PrebuiltSystem]:
        """Generate HP system options (mock data for demonstration)"""
        systems = []
        
        if use_case == "gaming" and budget >= 1500:
            systems.append(PrebuiltSystem(
                name="HP OMEN 45L Gaming Desktop",
                brand="HP",
                model="OMEN 45L",
                price=2299.0,
                specifications={
                    "cpu": "Intel Core i7-13700F",
                    "gpu": "NVIDIA GeForce RTX 4060",
                    "ram": "16GB DDR4",
                    "storage": "512GB NVMe SSD",
                    "motherboard": "HP Custom",
                    "psu": "500W"
                },
                url="https://www.hp.com/au-en/shop/product.aspx?id=omen-45l",
                image_url=None,
                availability="In Stock",
                store="HP Australia",
                use_case_fit=8.5,
                performance_score=7.5,
                value_score=7.0,
                pros=["Warranty included", "Pre-configured", "RGB lighting"],
                cons=["Limited upgrade options", "Proprietary components"],
                warranty_info="3 year warranty",
                is_clearance=False
            ))
        
        if use_case == "office" and budget >= 800:
            systems.append(PrebuiltSystem(
                name="HP Pavilion Desktop",
                brand="HP",
                model="Pavilion TP01",
                price=899.0,
                specifications={
                    "cpu": "Intel Core i5-13400",
                    "gpu": "Integrated Graphics",
                    "ram": "8GB DDR4",
                    "storage": "256GB SSD + 1TB HDD",
                    "motherboard": "HP Custom",
                    "psu": "300W"
                },
                url="https://www.hp.com/au-en/shop/product.aspx?id=pavilion-tp01",
                image_url=None,
                availability="In Stock",
                store="HP Australia",
                use_case_fit=8.0,
                performance_score=6.0,
                value_score=8.0,
                pros=["Good for office work", "Energy efficient", "Compact"],
                cons=["No gaming capability", "Limited RAM"],
                warranty_info="1 year warranty",
                is_clearance=False
            ))
        
        return systems
    
    def _generate_dell_systems(self, budget: float, use_case: str) -> List[PrebuiltSystem]:
        """Generate Dell system options"""
        systems = []
        
        if use_case == "gaming" and budget >= 2000:
            systems.append(PrebuiltSystem(
                name="Dell Alienware Aurora R15",
                brand="Dell",
                model="Aurora R15",
                price=2799.0,
                specifications={
                    "cpu": "Intel Core i7-13700F",
                    "gpu": "NVIDIA GeForce RTX 4070",
                    "ram": "16GB DDR5",
                    "storage": "1TB NVMe SSD",
                    "motherboard": "Dell Custom",
                    "psu": "750W"
                },
                url="https://www.dell.com/en-au/shop/alienware-aurora-r15",
                image_url=None,
                availability="In Stock",
                store="Dell Australia",
                use_case_fit=9.0,
                performance_score=8.5,
                value_score=6.5,
                pros=["High performance", "Premium build", "Liquid cooling"],
                cons=["Expensive", "Proprietary parts", "Large size"],
                warranty_info="2 year warranty",
                is_clearance=False
            ))
        
        if use_case == "workstation" and budget >= 1500:
            systems.append(PrebuiltSystem(
                name="Dell Precision 3660 Tower",
                brand="Dell",
                model="Precision 3660",
                price=1899.0,
                specifications={
                    "cpu": "Intel Core i7-13700",
                    "gpu": "NVIDIA RTX A2000",
                    "ram": "32GB DDR5",
                    "storage": "512GB NVMe SSD",
                    "motherboard": "Dell Custom",
                    "psu": "500W"
                },
                url="https://www.dell.com/en-au/shop/precision-3660",
                image_url=None,
                availability="In Stock",
                store="Dell Australia",
                use_case_fit=9.5,
                performance_score=8.0,
                value_score=7.5,
                pros=["Professional grade", "ISV certified", "ECC memory support"],
                cons=["Expensive", "Overkill for gaming"],
                warranty_info="3 year warranty",
                is_clearance=False
            ))
        
        return systems
    
    def _generate_lenovo_systems(self, budget: float, use_case: str) -> List[PrebuiltSystem]:
        """Generate Lenovo system options"""
        systems = []
        
        if use_case == "gaming" and budget >= 1800:
            systems.append(PrebuiltSystem(
                name="Lenovo Legion Tower 7i",
                brand="Lenovo",
                model="Legion Tower 7i",
                price=2199.0,
                specifications={
                    "cpu": "Intel Core i7-13700KF",
                    "gpu": "NVIDIA GeForce RTX 4060 Ti",
                    "ram": "16GB DDR5",
                    "storage": "1TB NVMe SSD",
                    "motherboard": "Lenovo Custom",
                    "psu": "650W"
                },
                url="https://www.lenovo.com/au/en/legion-tower-7i",
                image_url=None,
                availability="In Stock",
                store="Lenovo Australia",
                use_case_fit=8.8,
                performance_score=8.0,
                value_score=7.5,
                pros=["Good gaming performance", "RGB lighting", "Tool-less upgrades"],
                cons=["Some proprietary components", "Premium pricing"],
                warranty_info="2 year warranty",
                is_clearance=False
            ))
        
        return systems
    
    def _generate_australian_prebuilt_systems(self, store_name: str, budget: float, use_case: str) -> List[PrebuiltSystem]:
        """Generate Australian store pre-built systems"""
        systems = []
        
        if "Umart" in store_name and use_case == "gaming" and budget >= 1200:
            systems.append(PrebuiltSystem(
                name="Umart Gaming PC Ryzen 5 RTX 4060",
                brand="Umart",
                model="Gaming Build",
                price=1599.0,
                specifications={
                    "cpu": "AMD Ryzen 5 7600X",
                    "gpu": "NVIDIA GeForce RTX 4060",
                    "ram": "16GB DDR5",
                    "storage": "500GB NVMe SSD",
                    "motherboard": "B650 Chipset",
                    "psu": "650W 80+ Gold"
                },
                url="https://www.umart.com.au/gaming-pc-ryzen5-rtx4060",
                image_url=None,
                availability="In Stock",
                store=store_name,
                use_case_fit=8.5,
                performance_score=7.5,
                value_score=8.5,
                pros=["Great value", "Standard components", "Local support"],
                cons=["Basic case", "No RGB"],
                warranty_info="2 year warranty",
                is_clearance=False
            ))
        
        if "Scorptec" in store_name and use_case == "gaming" and budget >= 2000:
            systems.append(PrebuiltSystem(
                name="Scorptec Gaming PC i7 RTX 4070",
                brand="Scorptec",
                model="Gaming Elite",
                price=2399.0,
                specifications={
                    "cpu": "Intel Core i7-13700F",
                    "gpu": "NVIDIA GeForce RTX 4070",
                    "ram": "32GB DDR5",
                    "storage": "1TB NVMe SSD",
                    "motherboard": "Z690 Chipset",
                    "psu": "750W 80+ Gold"
                },
                url="https://www.scorptec.com.au/gaming-pc-i7-rtx4070",
                image_url=None,
                availability="In Stock",
                store=store_name,
                use_case_fit=9.0,
                performance_score=8.5,
                value_score=7.8,
                pros=["High performance", "Quality components", "RGB lighting"],
                cons=["Premium pricing", "Overkill for 1080p"],
                warranty_info="3 year warranty",
                is_clearance=False
            ))
        
        return systems
    
    def _filter_suitable_systems(self, systems: List[PrebuiltSystem], 
                               budget: float, use_case_specs: Dict) -> List[PrebuiltSystem]:
        """Filter systems that meet basic requirements"""
        suitable = []
        
        for system in systems:
            # Budget check (allow 10% over budget for exceptional value)
            if system.price <= budget * 1.1:
                # Basic spec requirements
                meets_requirements = True
                
                # Check minimum RAM requirement
                min_ram = use_case_specs.get("min_ram_gb", 8)
                system_ram = self._extract_ram_amount(system.specifications.get("ram", ""))
                if system_ram < min_ram:
                    meets_requirements = False
                
                # Check storage requirement
                min_storage = use_case_specs.get("min_storage_gb", 256)
                system_storage = self._extract_storage_amount(system.specifications.get("storage", ""))
                if system_storage < min_storage:
                    meets_requirements = False
                
                if meets_requirements:
                    suitable.append(system)
        
        return suitable
    
    def _rank_prebuilt_systems(self, systems: List[PrebuiltSystem], 
                             use_case: str, budget: float) -> List[PrebuiltSystem]:
        """Rank pre-built systems by overall score"""
        def calculate_score(system: PrebuiltSystem) -> float:
            score = (
                system.performance_score * 0.3 +
                system.value_score * 0.3 +
                system.use_case_fit * 0.25 +
                (10 if system.price <= budget else 5) * 0.15  # Budget fit
            )
            
            # Bonus for clearance items
            if system.is_clearance:
                score += 1.0
            
            # Penalty for going over budget
            if system.price > budget:
                over_budget_ratio = (system.price - budget) / budget
                score *= (1 - min(over_budget_ratio, 0.3))
            
            return score
        
        return sorted(systems, key=calculate_score, reverse=True)
    
    def _extract_ram_amount(self, ram_spec: str) -> int:
        """Extract RAM amount in GB from specification string"""
        match = re.search(r'(\d+)\s*gb', ram_spec.lower())
        return int(match.group(1)) if match else 8
    
    def _extract_storage_amount(self, storage_spec: str) -> int:
        """Extract storage amount in GB from specification string"""
        # Look for SSD first, then total storage
        total_gb = 0
        
        # Find all storage amounts
        gb_matches = re.findall(r'(\d+)\s*gb', storage_spec.lower())
        tb_matches = re.findall(r'(\d+)\s*tb', storage_spec.lower())
        
        for gb in gb_matches:
            total_gb += int(gb)
        
        for tb in tb_matches:
            total_gb += int(tb) * 1000
        
        return total_gb if total_gb > 0 else 256  # Default assumption
    
    def compare_prebuilt_to_custom(self, prebuilt: PrebuiltSystem, 
                                 custom_build_cost: float, 
                                 custom_build_performance: float) -> Dict[str, Any]:
        """Compare a pre-built system to a custom build"""
        comparison = {
            "prebuilt_advantages": [],
            "custom_advantages": [],
            "cost_difference": prebuilt.price - custom_build_cost,
            "performance_difference": prebuilt.performance_score - custom_build_performance,
            "recommendation": ""
        }
        
        # Cost comparison
        if prebuilt.price < custom_build_cost:
            comparison["prebuilt_advantages"].append(f"${custom_build_cost - prebuilt.price:.0f} cheaper")
        else:
            comparison["custom_advantages"].append(f"${prebuilt.price - custom_build_cost:.0f} cheaper")
        
        # Performance comparison
        if prebuilt.performance_score > custom_build_performance:
            comparison["prebuilt_advantages"].append("Better performance")
        else:
            comparison["custom_advantages"].append("Better performance")
        
        # Other advantages
        comparison["prebuilt_advantages"].extend([
            "Warranty included",
            "Pre-configured and tested",
            "Single point of support",
            "No assembly required"
        ])
        
        comparison["custom_advantages"].extend([
            "Component choice flexibility",
            "Better upgrade path",
            "No proprietary parts",
            "Learning experience"
        ])
        
        # Generate recommendation
        if prebuilt.price < custom_build_cost * 0.9 and prebuilt.performance_score >= custom_build_performance * 0.9:
            comparison["recommendation"] = "Pre-built offers excellent value"
        elif custom_build_cost < prebuilt.price * 0.9:
            comparison["recommendation"] = "Custom build offers better value"
        else:
            comparison["recommendation"] = "Both options are competitive"
        
        return comparison
