# PC Builder MCP Server

An intelligent Model Context Protocol (MCP) server that sources PC components from multiple Australian retailers, optimizes builds for specific use cases and budgets, and recommends the best value combinations including pre-built systems when advantageous.

## 🎯 Features

### **Intelligent Component Sourcing**
- **Multi-Store Optimization**: Sources components from 20+ Australian PC retailers
- **Real-Time Pricing**: Live price comparison across stores
- **Deal Detection**: Automatically identifies specials, clearance items, and best value deals
- **Cross-Store Builds**: Optimizes component selection across different stores for maximum value

### **Smart Build Optimization**
- **Use Case Analysis**: Tailored builds for gaming, workstation, content creation, AI/ML, office, and general use
- **Budget Intelligence**: Optimizes component allocation based on budget and use case priorities
- **Compatibility Checking**: Ensures all components work together (socket, power, form factor, etc.)
- **Performance Scoring**: Rates builds on performance, value, and use case fit

### **Pre-Built System Analysis**
- **Manufacturer Integration**: Analyzes systems from HP, Dell, Lenovo, and Australian system builders
- **Value Comparison**: Compares pre-built vs custom build value propositions
- **Clearance Detection**: Identifies when pre-built systems offer better value than custom builds

### **Advanced Capabilities**
- **Trend Analysis**: Considers current market trends and industry best practices
- **Future-Proofing**: Recommends builds with good upgrade paths
- **Compatibility Matrix**: Deep component compatibility checking
- **Multi-Budget Options**: Provides builds at different price points

## 🏪 Supported Australian Stores

### **Major PC Retailers**
- Umart, Scorptec, Mwave, Computer Alliance
- PC Case Gear, PLE Computers, Centre Com
- Techfast, Buck PCs, BPC Tech

### **Pre-Built System Sources**
- HP Australia, Dell Australia, Lenovo Australia
- Australian system builders and integrators

## 🛠 MCP Tools

### `build_pc`
Build a custom PC based on budget, use case, and requirements.

**Parameters:**
- `budget` (number): Budget in AUD
- `use_case` (string): Primary use case (gaming, workstation, office, content_creation, streaming, ai_ml, general)
- `preferences` (object): User preferences (brands, form factor, RGB, quiet operation, etc.)
- `requirements` (object): Specific requirements (min RAM, storage, target resolution/FPS, software)
- `include_prebuilt` (boolean): Whether to consider pre-built systems

**Example:**
```json
{
  "budget": 2500,
  "use_case": "gaming",
  "preferences": {
    "preferred_brands": ["AMD", "NVIDIA"],
    "form_factor": "atx",
    "rgb_lighting": true,
    "quiet_operation": false
  },
  "requirements": {
    "min_ram": 16,
    "target_resolution": "1440p",
    "target_fps": 144
  }
}
```

### `compare_components`
Compare specific components across stores.

**Parameters:**
- `component_type` (string): Type of component to compare
- `budget_range` (object): Min/max budget for comparison
- `specific_models` (array): Specific models to compare

### `check_compatibility`
Check compatibility between PC components.

**Parameters:**
- `components` (array): List of components with specifications

### `find_deals`
Find current deals and specials across Australian PC stores.

**Parameters:**
- `component_types` (array): Types of components to search for deals
- `min_discount` (number): Minimum discount percentage

### `analyze_prebuilt`
Analyze pre-built systems and compare to custom builds.

**Parameters:**
- `budget` (number): Budget for comparison
- `use_case` (string): Use case for analysis
- `include_brands` (array): Brands to include in analysis

### `optimize_build`
Optimize an existing build for better performance or value.

**Parameters:**
- `current_build` (array): Current build components
- `optimization_goal` (string): Goal (performance, value, efficiency, quiet)
- `budget_adjustment` (number): Additional budget available

## 🚀 Installation & Setup

### Prerequisites
- Python 3.8+
- OpenAI API key (optional, for enhanced extraction)

### Installation
```bash
# Clone or download the pc_builder_mcp folder
cd pc_builder_mcp

# Install dependencies
pip install -r requirements.txt

# Set up environment (optional)
export OPENAI_API_KEY="your-api-key-here"
```

### Running the MCP Server
```bash
# Start the MCP server
python server.py
```

### Integration with MCP Clients
The server implements the MCP protocol and can be integrated with any MCP-compatible client.

## 📊 Use Case Examples

### Gaming PC Build
```json
{
  "budget": 2000,
  "use_case": "gaming",
  "requirements": {
    "target_resolution": "1440p",
    "target_fps": 144,
    "min_ram": 16
  },
  "preferences": {
    "rgb_lighting": true,
    "preferred_brands": ["AMD", "NVIDIA"]
  }
}
```

**Expected Output:**
- Optimized component selection across multiple stores
- Performance score: 8.5/10
- Value score: 8.2/10
- Total cost: $1,950 AUD
- Components from 3-4 different stores for best value

### Workstation Build
```json
{
  "budget": 4000,
  "use_case": "workstation",
  "requirements": {
    "min_ram": 32,
    "specific_software": ["AutoCAD", "SolidWorks"]
  },
  "preferences": {
    "quiet_operation": true,
    "future_upgrade_path": true
  }
}
```

### Content Creation Build
```json
{
  "budget": 3500,
  "use_case": "content_creation",
  "requirements": {
    "min_ram": 32,
    "min_storage": 2000,
    "specific_software": ["Adobe Premiere Pro", "After Effects"]
  }
}
```

## 🔧 Architecture

### Core Components

1. **Component Scraper** (`component_scraper.py`)
   - Scrapes component data from Australian retailers
   - Extracts pricing, specifications, ratings, and deals
   - Handles rate limiting and respectful crawling

2. **Build Optimizer** (`build_optimizer.py`)
   - Generates optimized build configurations
   - Balances performance, value, and compatibility
   - Creates multiple build variants (balanced, performance, value)

3. **Compatibility Checker** (`compatibility_checker.py`)
   - Ensures component compatibility
   - Checks sockets, power requirements, form factors
   - Provides compatibility scores and warnings

4. **Prebuilt Analyzer** (`prebuilt_analyzer.py`)
   - Analyzes pre-built systems from major manufacturers
   - Compares pre-built vs custom build value
   - Identifies clearance and special offers

5. **Use Case Analyzer** (`use_case_analyzer.py`)
   - Defines specifications for different use cases
   - Adjusts requirements based on budget
   - Provides component recommendations

### Data Flow
1. **Request Analysis**: Parse user requirements and use case
2. **Component Sourcing**: Scrape latest pricing and availability
3. **Build Generation**: Create optimized build configurations
4. **Compatibility Check**: Ensure all components work together
5. **Prebuilt Comparison**: Compare with pre-built alternatives
6. **Ranking & Selection**: Rank options by overall score
7. **Response Formatting**: Present recommendations with detailed analysis

## 🎯 Optimization Strategies

### **Value Optimization**
- Prioritizes components with current deals and specials
- Balances performance per dollar across all components
- Considers total cost of ownership

### **Performance Optimization**
- Allocates budget to performance-critical components
- Ensures no bottlenecks in the system
- Optimizes for specific use case requirements

### **Compatibility Optimization**
- Checks socket compatibility (CPU/motherboard)
- Ensures adequate power supply capacity
- Verifies form factor compatibility
- Checks memory compatibility and speeds

### **Store Optimization**
- Sources components from multiple stores for best prices
- Considers shipping costs and store reliability
- Balances convenience vs savings

## 📈 Performance Metrics

The system provides detailed scoring for each build:

- **Performance Score** (0-10): Raw performance capability
- **Value Score** (0-10): Performance per dollar spent
- **Compatibility Score** (0-10): How well components work together
- **Use Case Fit** (0-10): How well the build matches intended use

## 🔮 Future Enhancements

- **Real-time Price Monitoring**: Track price changes over time
- **Stock Availability**: Real-time stock checking across stores
- **User Reviews Integration**: Include user reviews and ratings
- **Build Templates**: Pre-configured builds for common scenarios
- **Upgrade Path Analysis**: Recommendations for future upgrades
- **Power Consumption Analysis**: Detailed power usage calculations
- **Thermal Analysis**: Temperature and cooling considerations

## 📄 License

This project is designed for educational and personal use. Please respect the terms of service of the websites being scraped.

---

**Ready to build the perfect PC? Let the MCP server find the best deals across Australia!** 🇦🇺💻
