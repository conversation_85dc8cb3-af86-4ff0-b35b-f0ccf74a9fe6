#!/usr/bin/env python3
"""
Compatibility Checker for PC Builder MCP

Ensures PC components are compatible with each other, checking socket types,
power requirements, form factors, and other technical specifications.
"""

import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from component_scraper import ComponentData
from openrouter_client import OpenRouterClient

logger = logging.getLogger(__name__)

@dataclass
class CompatibilityIssue:
    """Represents a compatibility issue between components"""
    severity: str  # "error", "warning", "info"
    category: str
    message: str
    affected_components: List[str]
    suggestion: Optional[str] = None

class CompatibilityChecker:
    """Checks compatibility between PC components"""

    def __init__(self):
        self.openrouter_client = OpenRouterClient()

        # Socket compatibility mappings
        self.cpu_socket_compatibility = {
            # Intel sockets
            "LGA1700": ["12th gen", "13th gen", "14th gen", "i3-12", "i5-12", "i7-12", "i9-12",
                       "i3-13", "i5-13", "i7-13", "i9-13", "i3-14", "i5-14", "i7-14", "i9-14"],
            "LGA1200": ["10th gen", "11th gen", "i3-10", "i5-10", "i7-10", "i9-10",
                       "i3-11", "i5-11", "i7-11", "i9-11"],
            "LGA1151": ["8th gen", "9th gen", "i3-8", "i5-8", "i7-8", "i9-8",
                       "i3-9", "i5-9", "i7-9", "i9-9"],

            # AMD sockets
            "AM5": ["7000 series", "ryzen 7", "ryzen 9", "7600x", "7700x", "7800x", "7900x", "7950x"],
            "AM4": ["1000 series", "2000 series", "3000 series", "4000 series", "5000 series",
                   "ryzen 3", "ryzen 5", "ryzen 7", "ryzen 9", "1600", "2600", "3600", "5600"],
        }

        # Motherboard chipset compatibility
        self.chipset_compatibility = {
            # Intel chipsets
            "Z790": ["LGA1700"],
            "Z690": ["LGA1700"],
            "B660": ["LGA1700"],
            "H610": ["LGA1700"],
            "Z590": ["LGA1200"],
            "B560": ["LGA1200"],
            "H510": ["LGA1200"],

            # AMD chipsets
            "X670E": ["AM5"],
            "X670": ["AM5"],
            "B650E": ["AM5"],
            "B650": ["AM5"],
            "X570": ["AM4"],
            "B550": ["AM4"],
            "B450": ["AM4"],
            "A520": ["AM4"],
        }

        # RAM compatibility
        self.ram_compatibility = {
            "DDR5": ["AM5", "LGA1700"],
            "DDR4": ["AM4", "LGA1200", "LGA1151"],
        }

        # Form factor compatibility
        self.form_factor_compatibility = {
            "ATX": ["Full Tower", "Mid Tower", "ATX"],
            "Micro-ATX": ["Full Tower", "Mid Tower", "Micro-ATX", "ATX"],
            "Mini-ITX": ["Full Tower", "Mid Tower", "Mini-ITX", "Micro-ATX", "ATX"],
        }

        # Power supply requirements (estimated watts)
        self.component_power_requirements = {
            "video_cards": {
                "RTX 4090": 450,
                "RTX 4080": 320,
                "RTX 4070": 200,
                "RTX 4060": 115,
                "RTX 3080": 320,
                "RTX 3070": 220,
                "RTX 3060": 170,
                "RX 7900": 300,
                "RX 7800": 260,
                "RX 7700": 245,
                "RX 6800": 250,
                "RX 6700": 230,
            },
            "cpu": {
                "i9": 125,
                "i7": 65,
                "i5": 65,
                "i3": 65,
                "ryzen 9": 105,
                "ryzen 7": 65,
                "ryzen 5": 65,
                "ryzen 3": 65,
            }
        }

    def check_component_compatibility(self, component: ComponentData,
                                    existing_components: Dict[str, ComponentData]) -> bool:
        """Check if a component is compatible with existing components"""
        issues = self._check_compatibility_issues(component, existing_components)

        # Return True if no critical errors
        critical_issues = [issue for issue in issues if issue.severity == "error"]
        return len(critical_issues) == 0

    async def check_build_compatibility(self, components: Dict[str, ComponentData]) -> float:
        """Check overall build compatibility and return compatibility score (0-1)"""

        # Try OpenRouter LLM analysis first
        if self.openrouter_client.is_available():
            try:
                # Prepare component data for LLM
                component_summary = {}
                for category, component in components.items():
                    component_summary[category] = {
                        "name": component.name,
                        "brand": component.brand,
                        "specifications": component.specifications,
                        "category": component.category
                    }

                llm_analysis = await self.openrouter_client.analyze_build_compatibility(
                    component_summary, model_tier="fast"
                )

                if llm_analysis and "compatibility_score" in llm_analysis:
                    return llm_analysis["compatibility_score"]

            except Exception as e:
                logger.error(f"OpenRouter compatibility analysis failed: {str(e)}")

        # Fallback to rule-based compatibility checking
        all_issues = []

        # Check each component against all others
        for category, component in components.items():
            other_components = {k: v for k, v in components.items() if k != category}
            issues = self._check_compatibility_issues(component, other_components)
            all_issues.extend(issues)

        # Calculate compatibility score
        error_count = len([issue for issue in all_issues if issue.severity == "error"])
        warning_count = len([issue for issue in all_issues if issue.severity == "warning"])

        # Perfect score if no issues
        if not all_issues:
            return 1.0

        # Deduct points for issues
        score = 1.0
        score -= error_count * 0.3  # Major deduction for errors
        score -= warning_count * 0.1  # Minor deduction for warnings

        return max(score, 0.0)

    def _check_compatibility_issues(self, component: ComponentData,
                                  existing_components: Dict[str, ComponentData]) -> List[CompatibilityIssue]:
        """Check for compatibility issues between a component and existing components"""
        issues = []

        if component.category == "cpu":
            issues.extend(self._check_cpu_compatibility(component, existing_components))
        elif component.category == "motherboard":
            issues.extend(self._check_motherboard_compatibility(component, existing_components))
        elif component.category == "ram":
            issues.extend(self._check_ram_compatibility(component, existing_components))
        elif component.category == "video_cards":
            issues.extend(self._check_gpu_compatibility(component, existing_components))
        elif component.category == "psu":
            issues.extend(self._check_psu_compatibility(component, existing_components))
        elif component.category == "case":
            issues.extend(self._check_case_compatibility(component, existing_components))

        return issues

    def _check_cpu_compatibility(self, cpu: ComponentData,
                               existing_components: Dict[str, ComponentData]) -> List[CompatibilityIssue]:
        """Check CPU compatibility with motherboard and other components"""
        issues = []

        if "motherboard" in existing_components:
            motherboard = existing_components["motherboard"]

            # Extract socket information
            cpu_socket = self._extract_cpu_socket(cpu)
            motherboard_socket = self._extract_motherboard_socket(motherboard)

            if cpu_socket and motherboard_socket:
                if cpu_socket != motherboard_socket:
                    issues.append(CompatibilityIssue(
                        severity="error",
                        category="socket",
                        message=f"CPU socket {cpu_socket} incompatible with motherboard socket {motherboard_socket}",
                        affected_components=["cpu", "motherboard"],
                        suggestion="Choose a CPU and motherboard with matching sockets"
                    ))

        # Check RAM compatibility
        if "ram" in existing_components:
            ram = existing_components["ram"]
            ram_type = self._extract_ram_type(ram)
            cpu_ram_support = self._get_cpu_ram_support(cpu)

            if ram_type and cpu_ram_support and ram_type not in cpu_ram_support:
                issues.append(CompatibilityIssue(
                    severity="error",
                    category="memory",
                    message=f"CPU does not support {ram_type} memory",
                    affected_components=["cpu", "ram"],
                    suggestion=f"Use {', '.join(cpu_ram_support)} memory instead"
                ))

        return issues

    def _check_motherboard_compatibility(self, motherboard: ComponentData,
                                       existing_components: Dict[str, ComponentData]) -> List[CompatibilityIssue]:
        """Check motherboard compatibility"""
        issues = []

        # Check form factor with case
        if "case" in existing_components:
            case = existing_components["case"]
            motherboard_form_factor = self._extract_motherboard_form_factor(motherboard)
            case_support = self._extract_case_form_factor_support(case)

            if motherboard_form_factor and case_support:
                if motherboard_form_factor not in case_support:
                    issues.append(CompatibilityIssue(
                        severity="error",
                        category="form_factor",
                        message=f"Motherboard form factor {motherboard_form_factor} not supported by case",
                        affected_components=["motherboard", "case"],
                        suggestion="Choose a larger case or smaller motherboard"
                    ))

        return issues

    def _check_ram_compatibility(self, ram: ComponentData,
                               existing_components: Dict[str, ComponentData]) -> List[CompatibilityIssue]:
        """Check RAM compatibility"""
        issues = []

        ram_type = self._extract_ram_type(ram)
        ram_speed = self._extract_ram_speed(ram)

        # Check with motherboard
        if "motherboard" in existing_components:
            motherboard = existing_components["motherboard"]
            supported_ram_types = self._get_motherboard_ram_support(motherboard)
            max_ram_speed = self._get_motherboard_max_ram_speed(motherboard)

            if ram_type and supported_ram_types and ram_type not in supported_ram_types:
                issues.append(CompatibilityIssue(
                    severity="error",
                    category="memory",
                    message=f"Motherboard does not support {ram_type}",
                    affected_components=["ram", "motherboard"]
                ))

            if ram_speed and max_ram_speed and ram_speed > max_ram_speed:
                issues.append(CompatibilityIssue(
                    severity="warning",
                    category="memory",
                    message=f"RAM speed {ram_speed}MHz exceeds motherboard maximum {max_ram_speed}MHz",
                    affected_components=["ram", "motherboard"],
                    suggestion="RAM will run at motherboard's maximum speed"
                ))

        return issues

    def _check_gpu_compatibility(self, gpu: ComponentData,
                               existing_components: Dict[str, ComponentData]) -> List[CompatibilityIssue]:
        """Check GPU compatibility"""
        issues = []

        # Check power requirements
        if "psu" in existing_components:
            psu = existing_components["psu"]
            gpu_power = self._estimate_gpu_power_requirement(gpu)
            psu_wattage = self._extract_psu_wattage(psu)

            if gpu_power and psu_wattage:
                # Estimate total system power (GPU + CPU + other components)
                total_estimated_power = gpu_power + 150  # Base system + CPU estimate

                if "cpu" in existing_components:
                    cpu_power = self._estimate_cpu_power_requirement(existing_components["cpu"])
                    total_estimated_power = gpu_power + cpu_power + 100  # Base system

                if total_estimated_power > psu_wattage * 0.8:  # 80% PSU efficiency rule
                    issues.append(CompatibilityIssue(
                        severity="warning" if total_estimated_power <= psu_wattage else "error",
                        category="power",
                        message=f"PSU may be insufficient. Estimated power: {total_estimated_power}W, PSU: {psu_wattage}W",
                        affected_components=["video_cards", "psu"],
                        suggestion=f"Consider a PSU with at least {int(total_estimated_power * 1.2)}W"
                    ))

        # Check case clearance (simplified)
        if "case" in existing_components:
            case = existing_components["case"]
            gpu_length = self._estimate_gpu_length(gpu)
            case_gpu_clearance = self._extract_case_gpu_clearance(case)

            if gpu_length and case_gpu_clearance and gpu_length > case_gpu_clearance:
                issues.append(CompatibilityIssue(
                    severity="error",
                    category="clearance",
                    message="GPU may not fit in case",
                    affected_components=["video_cards", "case"],
                    suggestion="Choose a larger case or smaller GPU"
                ))

        return issues

    def _check_psu_compatibility(self, psu: ComponentData,
                               existing_components: Dict[str, ComponentData]) -> List[CompatibilityIssue]:
        """Check PSU compatibility"""
        issues = []

        psu_wattage = self._extract_psu_wattage(psu)

        if psu_wattage:
            # Calculate total system power requirements
            total_power = 100  # Base system power

            if "cpu" in existing_components:
                cpu_power = self._estimate_cpu_power_requirement(existing_components["cpu"])
                total_power += cpu_power

            if "video_cards" in existing_components:
                gpu_power = self._estimate_gpu_power_requirement(existing_components["video_cards"])
                total_power += gpu_power

            # Add power for other components
            total_power += len(existing_components) * 20  # Rough estimate for other components

            if total_power > psu_wattage * 0.8:
                issues.append(CompatibilityIssue(
                    severity="warning" if total_power <= psu_wattage else "error",
                    category="power",
                    message=f"PSU wattage may be insufficient for system requirements",
                    affected_components=["psu"] + list(existing_components.keys()),
                    suggestion=f"Consider a PSU with at least {int(total_power * 1.2)}W"
                ))

        return issues

    def _check_case_compatibility(self, case: ComponentData,
                                existing_components: Dict[str, ComponentData]) -> List[CompatibilityIssue]:
        """Check case compatibility"""
        issues = []

        # Check motherboard form factor support
        if "motherboard" in existing_components:
            motherboard = existing_components["motherboard"]
            motherboard_form_factor = self._extract_motherboard_form_factor(motherboard)
            case_support = self._extract_case_form_factor_support(case)

            if motherboard_form_factor and case_support:
                if motherboard_form_factor not in case_support:
                    issues.append(CompatibilityIssue(
                        severity="error",
                        category="form_factor",
                        message=f"Case does not support {motherboard_form_factor} motherboards",
                        affected_components=["case", "motherboard"]
                    ))

        return issues

    # Helper methods for extracting component specifications
    def _extract_cpu_socket(self, cpu: ComponentData) -> Optional[str]:
        """Extract CPU socket from component data"""
        cpu_text = f"{cpu.name} {cpu.model}".lower()

        # Look for socket indicators
        if any(indicator in cpu_text for indicator in ["lga1700", "1700"]):
            return "LGA1700"
        elif any(indicator in cpu_text for indicator in ["lga1200", "1200"]):
            return "LGA1200"
        elif any(indicator in cpu_text for indicator in ["am5"]):
            return "AM5"
        elif any(indicator in cpu_text for indicator in ["am4"]):
            return "AM4"

        # Infer from CPU generation
        if any(gen in cpu_text for gen in ["12th", "13th", "14th"]):
            return "LGA1700"
        elif any(gen in cpu_text for gen in ["10th", "11th"]):
            return "LGA1200"
        elif "7000" in cpu_text or any(model in cpu_text for model in ["7600", "7700", "7800", "7900", "7950"]):
            return "AM5"
        elif any(series in cpu_text for series in ["5000", "3000", "2000", "1000"]):
            return "AM4"

        return None

    def _extract_motherboard_socket(self, motherboard: ComponentData) -> Optional[str]:
        """Extract motherboard socket from component data"""
        motherboard_text = f"{motherboard.name} {motherboard.model}".lower()

        if "lga1700" in motherboard_text or "1700" in motherboard_text:
            return "LGA1700"
        elif "lga1200" in motherboard_text or "1200" in motherboard_text:
            return "LGA1200"
        elif "am5" in motherboard_text:
            return "AM5"
        elif "am4" in motherboard_text:
            return "AM4"

        return None

    def _extract_ram_type(self, ram: ComponentData) -> Optional[str]:
        """Extract RAM type (DDR4/DDR5) from component data"""
        ram_text = f"{ram.name} {ram.model}".lower()

        if "ddr5" in ram_text:
            return "DDR5"
        elif "ddr4" in ram_text:
            return "DDR4"

        return ram.specifications.get("type")

    def _extract_ram_speed(self, ram: ComponentData) -> Optional[int]:
        """Extract RAM speed from component data"""
        speed = ram.specifications.get("speed_mhz")
        if speed:
            return speed

        # Try to extract from name
        ram_text = f"{ram.name} {ram.model}"
        speed_match = re.search(r'(\d{4,5})\s*mhz', ram_text.lower())
        if speed_match:
            return int(speed_match.group(1))

        return None

    def _extract_motherboard_form_factor(self, motherboard: ComponentData) -> Optional[str]:
        """Extract motherboard form factor"""
        motherboard_text = f"{motherboard.name} {motherboard.model}".lower()

        if "mini-itx" in motherboard_text or "mini itx" in motherboard_text:
            return "Mini-ITX"
        elif "micro-atx" in motherboard_text or "micro atx" in motherboard_text or "matx" in motherboard_text:
            return "Micro-ATX"
        elif "atx" in motherboard_text:
            return "ATX"

        return "ATX"  # Default assumption

    def _extract_case_form_factor_support(self, case: ComponentData) -> List[str]:
        """Extract supported form factors from case"""
        case_text = f"{case.name} {case.model}".lower()

        supported = []
        if "atx" in case_text:
            supported.extend(["ATX", "Micro-ATX", "Mini-ITX"])
        elif "micro" in case_text:
            supported.extend(["Micro-ATX", "Mini-ITX"])
        elif "mini" in case_text:
            supported.append("Mini-ITX")
        else:
            # Default assumption for mid/full tower
            supported.extend(["ATX", "Micro-ATX", "Mini-ITX"])

        return supported

    def _extract_psu_wattage(self, psu: ComponentData) -> Optional[int]:
        """Extract PSU wattage"""
        wattage = psu.specifications.get("wattage")
        if wattage:
            return wattage

        # Try to extract from name
        psu_text = f"{psu.name} {psu.model}"
        wattage_match = re.search(r'(\d{3,4})\s*w', psu_text.lower())
        if wattage_match:
            return int(wattage_match.group(1))

        return None

    def _estimate_gpu_power_requirement(self, gpu: ComponentData) -> int:
        """Estimate GPU power requirement"""
        gpu_text = f"{gpu.name} {gpu.model}".lower()

        for model, power in self.component_power_requirements["video_cards"].items():
            if model.lower() in gpu_text:
                return power

        # Default estimates based on tier
        if any(tier in gpu_text for tier in ["4090", "7900 xtx"]):
            return 400
        elif any(tier in gpu_text for tier in ["4080", "7900 xt", "3080"]):
            return 300
        elif any(tier in gpu_text for tier in ["4070", "7800", "3070"]):
            return 220
        elif any(tier in gpu_text for tier in ["4060", "7700", "3060"]):
            return 150
        else:
            return 200  # Conservative estimate

    def _estimate_cpu_power_requirement(self, cpu: ComponentData) -> int:
        """Estimate CPU power requirement"""
        cpu_text = f"{cpu.name} {cpu.model}".lower()

        for model, power in self.component_power_requirements["cpu"].items():
            if model.lower() in cpu_text:
                return power

        return 65  # Default TDP for most modern CPUs

    def _estimate_gpu_length(self, gpu: ComponentData) -> Optional[int]:
        """Estimate GPU length in mm"""
        # This would typically be extracted from detailed specifications
        # For now, return conservative estimates based on tier
        gpu_text = f"{gpu.name} {gpu.model}".lower()

        if any(tier in gpu_text for tier in ["4090", "7900 xtx"]):
            return 340  # Large cards
        elif any(tier in gpu_text for tier in ["4080", "4070", "7900", "7800"]):
            return 300  # Mid-large cards
        else:
            return 250  # Standard cards

    def _extract_case_gpu_clearance(self, case: ComponentData) -> Optional[int]:
        """Extract maximum GPU clearance from case"""
        # This would typically be in detailed specifications
        # For now, return estimates based on case type
        case_text = f"{case.name} {case.model}".lower()

        if "full tower" in case_text:
            return 400
        elif "mid tower" in case_text:
            return 350
        elif "micro" in case_text:
            return 300
        elif "mini" in case_text:
            return 250
        else:
            return 350  # Default assumption

    def _get_cpu_ram_support(self, cpu: ComponentData) -> List[str]:
        """Get supported RAM types for CPU"""
        cpu_socket = self._extract_cpu_socket(cpu)

        if cpu_socket in ["AM5", "LGA1700"]:
            return ["DDR5", "DDR4"]  # Many support both
        elif cpu_socket in ["AM4", "LGA1200", "LGA1151"]:
            return ["DDR4"]

        return ["DDR4"]  # Safe default

    def _get_motherboard_ram_support(self, motherboard: ComponentData) -> List[str]:
        """Get supported RAM types for motherboard"""
        motherboard_text = f"{motherboard.name} {motherboard.model}".lower()

        if "ddr5" in motherboard_text:
            return ["DDR5"]
        elif "ddr4" in motherboard_text:
            return ["DDR4"]

        # Infer from socket
        socket = self._extract_motherboard_socket(motherboard)
        if socket in ["AM5", "LGA1700"]:
            return ["DDR5", "DDR4"]
        else:
            return ["DDR4"]

    def _get_motherboard_max_ram_speed(self, motherboard: ComponentData) -> Optional[int]:
        """Get maximum supported RAM speed for motherboard"""
        # This would typically be in detailed specifications
        # For now, return conservative estimates
        socket = self._extract_motherboard_socket(motherboard)

        if socket in ["AM5", "LGA1700"]:
            return 5600  # Modern platforms support high speeds
        elif socket in ["AM4", "LGA1200"]:
            return 3200  # Older platforms
        else:
            return 2666  # Conservative default
