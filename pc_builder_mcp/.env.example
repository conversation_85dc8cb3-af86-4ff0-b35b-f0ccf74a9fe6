# PC Builder MCP Environment Configuration

# OpenRouter API Configuration
OPENROUTER_API_KEY=your-openrouter-api-key-here

# Optional: Preferred model tiers for different tasks
# Options: fast, balanced, powerful, coding, cheap
OPENROUTER_SPEC_EXTRACTION_MODEL=fast
OPENROUTER_COMPATIBILITY_MODEL=balanced
OPENROUTER_BUILD_RECOMMENDATION_MODEL=balanced
OPENROUTER_MARKET_ANALYSIS_MODEL=powerful

# Optional: Enable/disable LLM features
ENABLE_LLM_SPEC_EXTRACTION=true
ENABLE_LLM_COMPATIBILITY_CHECK=true
ENABLE_LLM_BUILD_OPTIMIZATION=true
ENABLE_LLM_MARKET_ANALYSIS=true

# Scraping Configuration
MAX_PAGES_PER_STORE=2
SCRAPING_DELAY_SECONDS=3
CACHE_DURATION_HOURS=6

# Component Filtering
MIN_COMPONENT_RATING=3.0
MAX_COMPONENTS_PER_CATEGORY=50

# Build Optimization
MAX_BUILDS_TO_GENERATE=5
COMPATIBILITY_THRESHOLD=0.8

# Debug and Logging
LOG_LEVEL=INFO
ENABLE_DEBUG_MODE=false
