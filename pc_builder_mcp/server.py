#!/usr/bin/env python3
"""
PC Builder MCP Server

An intelligent MCP server that sources PC components from multiple Australian retailers,
optimizes builds for specific use cases and budgets, and recommends the best value
combinations including pre-built systems when advantageous.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
try:
    import mcp.types as types
    from mcp.server import Server
    from mcp.server.models import InitializationOptions
    import mcp.server.stdio
    MCP_AVAILABLE = True
except ImportError:
    print("MCP not available. Install with: pip install mcp")
    MCP_AVAILABLE = False
    # Create mock classes for development
    class types:
        class Tool:
            def __init__(self, name, description, inputSchema):
                self.name = name
                self.description = description
                self.inputSchema = inputSchema
        class TextContent:
            def __init__(self, type, text):
                self.type = type
                self.text = text

    class Server:
        def __init__(self, name):
            self.name = name
        def list_tools(self): pass
        def call_tool(self): pass

from component_scraper import ComponentScraper
from build_optimizer import BuildOptimizer
from prebuilt_analyzer import Prebuilt<PERSON><PERSON>yzer
from compatibility_checker import CompatibilityChecker
from use_case_analyzer import UseCaseAnalyzer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class BuildRequest:
    """User's PC build request"""
    budget: float
    use_case: str  # gaming, workstation, office, content_creation, etc.
    preferences: Dict[str, Any]  # brand preferences, form factor, etc.
    requirements: Dict[str, Any]  # specific requirements like min RAM, storage, etc.
    location: str = "Australia"
    include_prebuilt: bool = True

@dataclass
class ComponentRecommendation:
    """Individual component recommendation"""
    category: str
    name: str
    brand: str
    model: str
    price: float
    store: str
    url: str
    specifications: Dict[str, Any]
    rating: Optional[float]
    reviews_count: Optional[int]
    compatibility_score: float
    value_score: float
    reason: str  # Why this component was chosen

@dataclass
class BuildRecommendation:
    """Complete build recommendation"""
    build_type: str  # custom or prebuilt
    total_cost: float
    components: List[ComponentRecommendation]
    compatibility_score: float
    performance_score: float
    value_score: float
    use_case_fit: float
    pros: List[str]
    cons: List[str]
    build_notes: str
    stores_used: List[str]

class PCBuilderMCPServer:
    """Main MCP server for PC building"""

    def __init__(self):
        self.server = Server("pc-builder")
        self.component_scraper = ComponentScraper()
        self.build_optimizer = BuildOptimizer()
        self.prebuilt_analyzer = PrebuiltAnalyzer()
        self.compatibility_checker = CompatibilityChecker()
        self.use_case_analyzer = UseCaseAnalyzer()

        # Cache for component data
        self.component_cache = {}
        self.cache_expiry = {}
        self.cache_duration = timedelta(hours=6)  # Cache for 6 hours

        self._setup_handlers()

    def _setup_handlers(self):
        """Setup MCP handlers"""

        @self.server.list_tools()
        async def handle_list_tools() -> list[types.Tool]:
            """List available tools"""
            return [
                types.Tool(
                    name="build_pc",
                    description="Build a custom PC based on budget, use case, and requirements",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "budget": {
                                "type": "number",
                                "description": "Budget in AUD"
                            },
                            "use_case": {
                                "type": "string",
                                "enum": ["gaming", "workstation", "office", "content_creation", "streaming", "ai_ml", "general"],
                                "description": "Primary use case for the PC"
                            },
                            "preferences": {
                                "type": "object",
                                "description": "User preferences (brand, form factor, etc.)",
                                "properties": {
                                    "preferred_brands": {"type": "array", "items": {"type": "string"}},
                                    "form_factor": {"type": "string", "enum": ["atx", "micro_atx", "mini_itx"]},
                                    "rgb_lighting": {"type": "boolean"},
                                    "quiet_operation": {"type": "boolean"},
                                    "future_upgrade_path": {"type": "boolean"}
                                }
                            },
                            "requirements": {
                                "type": "object",
                                "description": "Specific requirements",
                                "properties": {
                                    "min_ram": {"type": "number"},
                                    "min_storage": {"type": "number"},
                                    "target_resolution": {"type": "string"},
                                    "target_fps": {"type": "number"},
                                    "specific_software": {"type": "array", "items": {"type": "string"}}
                                }
                            },
                            "include_prebuilt": {
                                "type": "boolean",
                                "description": "Whether to consider pre-built systems",
                                "default": True
                            }
                        },
                        "required": ["budget", "use_case"]
                    }
                ),
                types.Tool(
                    name="compare_components",
                    description="Compare specific components across stores",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "component_type": {
                                "type": "string",
                                "enum": ["video_cards", "cpu", "motherboard", "ram", "storage", "psu", "case", "cooling"]
                            },
                            "budget_range": {
                                "type": "object",
                                "properties": {
                                    "min": {"type": "number"},
                                    "max": {"type": "number"}
                                }
                            },
                            "specific_models": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Specific models to compare"
                            }
                        },
                        "required": ["component_type"]
                    }
                ),
                types.Tool(
                    name="check_compatibility",
                    description="Check compatibility between PC components",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "components": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "category": {"type": "string"},
                                        "model": {"type": "string"},
                                        "specifications": {"type": "object"}
                                    }
                                }
                            }
                        },
                        "required": ["components"]
                    }
                ),
                types.Tool(
                    name="find_deals",
                    description="Find current deals and specials across Australian PC stores",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "component_types": {
                                "type": "array",
                                "items": {"type": "string"}
                            },
                            "min_discount": {
                                "type": "number",
                                "description": "Minimum discount percentage"
                            }
                        }
                    }
                ),
                types.Tool(
                    name="analyze_prebuilt",
                    description="Analyze pre-built systems and compare to custom builds",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "budget": {"type": "number"},
                            "use_case": {"type": "string"},
                            "include_brands": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Brands to include (HP, Dell, Lenovo, etc.)"
                            }
                        },
                        "required": ["budget", "use_case"]
                    }
                ),
                types.Tool(
                    name="optimize_build",
                    description="Optimize an existing build for better performance or value",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "current_build": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "category": {"type": "string"},
                                        "model": {"type": "string"},
                                        "price": {"type": "number"}
                                    }
                                }
                            },
                            "optimization_goal": {
                                "type": "string",
                                "enum": ["performance", "value", "efficiency", "quiet"]
                            },
                            "budget_adjustment": {"type": "number"}
                        },
                        "required": ["current_build", "optimization_goal"]
                    }
                )
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: dict) -> list[types.TextContent]:
            """Handle tool calls"""
            try:
                if name == "build_pc":
                    return await self._build_pc(arguments)
                elif name == "compare_components":
                    return await self._compare_components(arguments)
                elif name == "check_compatibility":
                    return await self._check_compatibility(arguments)
                elif name == "find_deals":
                    return await self._find_deals(arguments)
                elif name == "analyze_prebuilt":
                    return await self._analyze_prebuilt(arguments)
                elif name == "optimize_build":
                    return await self._optimize_build(arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
            except Exception as e:
                logger.error(f"Error in tool {name}: {str(e)}")
                return [types.TextContent(
                    type="text",
                    text=f"Error executing {name}: {str(e)}"
                )]

    async def _build_pc(self, arguments: dict) -> list[types.TextContent]:
        """Build a custom PC based on requirements"""
        request = BuildRequest(
            budget=arguments["budget"],
            use_case=arguments["use_case"],
            preferences=arguments.get("preferences", {}),
            requirements=arguments.get("requirements", {}),
            include_prebuilt=arguments.get("include_prebuilt", True)
        )

        # Get fresh component data
        await self._refresh_component_data()

        # Analyze use case requirements
        use_case_specs = self.use_case_analyzer.analyze_use_case(
            request.use_case, request.budget, request.requirements
        )

        # Generate custom build recommendations
        custom_builds = await self.build_optimizer.generate_builds(
            request, use_case_specs, self.component_cache
        )

        # Analyze pre-built options if requested
        prebuilt_options = []
        if request.include_prebuilt:
            prebuilt_options = await self.prebuilt_analyzer.find_prebuilt_options(
                request.budget, request.use_case, use_case_specs
            )

        # Combine and rank all options
        all_options = custom_builds + prebuilt_options
        ranked_options = self._rank_build_options(all_options, request)

        # Format response
        response = self._format_build_response(ranked_options, request)

        return [types.TextContent(type="text", text=response)]

    async def _refresh_component_data(self):
        """Refresh component data from all stores"""
        current_time = datetime.now()

        # Check if cache is still valid
        if (self.component_cache and
            self.cache_expiry.get('last_update', datetime.min) + self.cache_duration > current_time):
            return

        logger.info("Refreshing component data from Australian stores...")

        # Scrape latest data from all stores
        fresh_data = await self.component_scraper.scrape_all_stores()

        self.component_cache = fresh_data
        self.cache_expiry['last_update'] = current_time

        logger.info(f"Component data refreshed. Found {sum(len(cat) for cat in fresh_data.values())} components")

    def _rank_build_options(self, options: List[BuildRecommendation], request: BuildRequest) -> List[BuildRecommendation]:
        """Rank build options based on multiple criteria"""
        def calculate_score(build: BuildRecommendation) -> float:
            # Weighted scoring based on user priorities
            score = (
                build.value_score * 0.3 +
                build.performance_score * 0.25 +
                build.use_case_fit * 0.25 +
                build.compatibility_score * 0.2
            )

            # Bonus for staying within budget
            if build.total_cost <= request.budget:
                score += 0.1
            else:
                # Penalty for going over budget
                over_budget_ratio = (build.total_cost - request.budget) / request.budget
                score -= min(0.3, over_budget_ratio)

            return score

        # Sort by calculated score
        return sorted(options, key=calculate_score, reverse=True)

    def _format_build_response(self, builds: List[BuildRecommendation], request: BuildRequest) -> str:
        """Format the build recommendations into a readable response"""
        if not builds:
            return "No suitable builds found for your requirements."

        response = f"# PC Build Recommendations for {request.use_case.title()} (Budget: ${request.budget:,.0f} AUD)\n\n"

        for i, build in enumerate(builds[:3], 1):  # Show top 3 recommendations
            response += f"## Option {i}: {build.build_type.title()} Build\n"
            response += f"**Total Cost:** ${build.total_cost:,.0f} AUD\n"
            response += f"**Performance Score:** {build.performance_score:.1f}/10\n"
            response += f"**Value Score:** {build.value_score:.1f}/10\n"
            response += f"**Use Case Fit:** {build.use_case_fit:.1f}/10\n\n"

            if build.build_type == "custom":
                response += "### Components:\n"
                for component in build.components:
                    response += f"- **{component.category.title()}:** {component.name} - ${component.price:.0f} ({component.store})\n"
                    response += f"  *{component.reason}*\n"
            else:
                response += f"### Pre-built System:\n{build.build_notes}\n"

            response += f"\n**Pros:** {', '.join(build.pros)}\n"
            response += f"**Cons:** {', '.join(build.cons)}\n"
            response += f"**Stores:** {', '.join(build.stores_used)}\n\n"
            response += "---\n\n"

        return response

async def main():
    """Run the MCP server"""
    if not MCP_AVAILABLE:
        print("MCP framework not available. Running in test mode...")
        print("Install MCP with: pip install mcp")
        print("For now, you can test the functionality with test_server.py")
        return

    server_instance = PCBuilderMCPServer()

    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server_instance.server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="pc-builder",
                server_version="1.0.0",
                capabilities=server_instance.server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities=None,
                ),
            ),
        )

if __name__ == "__main__":
    asyncio.run(main())
