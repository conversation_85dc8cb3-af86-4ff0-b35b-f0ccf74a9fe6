# PC Builder MCP Server Requirements

# MCP Framework
mcp>=1.0.0

# Web scraping and HTTP
crawl4ai>=0.2.77
aiohttp>=3.8.0
beautifulsoup4>=4.12.0
lxml>=4.9.0
requests>=2.31.0

# Data processing
pandas>=2.0.0
pydantic>=2.0.0

# Async utilities
asyncio-throttle>=1.0.2

# Environment and configuration
python-dotenv>=1.0.0

# Logging and utilities
rich>=13.0.0

# Optional: For enhanced LLM extraction
openai>=1.0.0

# Development and testing
pytest>=7.0.0
pytest-asyncio>=0.21.0
