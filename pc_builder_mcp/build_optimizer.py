#!/usr/bin/env python3
"""
Build Optimizer for PC Builder MCP

Intelligently optimizes PC builds by selecting the best components across multiple stores,
considering compatibility, performance, value, and user preferences.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import itertools
from component_scraper import ComponentData
from compatibility_checker import Compati<PERSON><PERSON><PERSON><PERSON>
from openrouter_client import OpenRouterClient

logger = logging.getLogger(__name__)

@dataclass
class BuildConfiguration:
    """Configuration for a PC build"""
    components: Dict[str, ComponentData]
    total_cost: float
    performance_score: float
    value_score: float
    compatibility_score: float
    use_case_fit: float
    stores_used: List[str]
    pros: List[str]
    cons: List[str]
    build_notes: str

class BuildOptimizer:
    """Optimizes PC builds across multiple stores and components"""

    def __init__(self):
        self.compatibility_checker = CompatibilityChecker()
        self.openrouter_client = OpenRouterClient()

        # Component priority weights for different use cases
        self.use_case_weights = {
            "gaming": {
                "video_cards": 0.35,
                "cpu": 0.25,
                "ram": 0.15,
                "storage": 0.10,
                "motherboard": 0.08,
                "psu": 0.05,
                "case": 0.02
            },
            "workstation": {
                "cpu": 0.35,
                "ram": 0.25,
                "storage": 0.15,
                "video_cards": 0.10,
                "motherboard": 0.10,
                "psu": 0.05
            },
            "content_creation": {
                "cpu": 0.30,
                "video_cards": 0.25,
                "ram": 0.20,
                "storage": 0.15,
                "motherboard": 0.08,
                "psu": 0.02
            },
            "office": {
                "cpu": 0.30,
                "ram": 0.25,
                "storage": 0.20,
                "motherboard": 0.15,
                "case": 0.05,
                "psu": 0.05
            },
            "ai_ml": {
                "video_cards": 0.40,
                "cpu": 0.25,
                "ram": 0.20,
                "storage": 0.10,
                "motherboard": 0.05
            }
        }

        # Budget allocation percentages for different use cases
        self.budget_allocation = {
            "gaming": {
                "video_cards": 0.40,
                "cpu": 0.25,
                "ram": 0.10,
                "storage": 0.08,
                "motherboard": 0.10,
                "psu": 0.05,
                "case": 0.02
            },
            "workstation": {
                "cpu": 0.35,
                "ram": 0.20,
                "storage": 0.15,
                "video_cards": 0.15,
                "motherboard": 0.10,
                "psu": 0.05
            },
            "content_creation": {
                "cpu": 0.30,
                "video_cards": 0.25,
                "ram": 0.15,
                "storage": 0.15,
                "motherboard": 0.10,
                "psu": 0.05
            }
        }

    async def generate_builds(self, request, use_case_specs: Dict, component_data: Dict[str, List[ComponentData]]) -> List[BuildConfiguration]:
        """Generate optimized build configurations with OpenRouter intelligence"""
        builds = []

        # Try OpenRouter-enhanced build generation first
        if self.openrouter_client.is_available():
            try:
                llm_builds = await self._generate_llm_enhanced_builds(request, use_case_specs, component_data)
                builds.extend(llm_builds)
            except Exception as e:
                logger.error(f"OpenRouter build generation failed: {str(e)}")

        # Fallback to traditional build generation
        if not builds:
            # Calculate budget allocation for this use case
            allocation = self.budget_allocation.get(request.use_case, self.budget_allocation["gaming"])
            component_budgets = {category: request.budget * percentage for category, percentage in allocation.items()}

            # Generate multiple build variants
            builds.extend(await self._generate_balanced_build(request, component_budgets, component_data, use_case_specs))
            builds.extend(await self._generate_performance_build(request, component_budgets, component_data, use_case_specs))
            builds.extend(await self._generate_value_build(request, component_budgets, component_data, use_case_specs))

        # Filter and rank builds
        valid_builds = [build for build in builds if build.compatibility_score >= 0.8]
        valid_builds.sort(key=lambda x: self._calculate_overall_score(x, request), reverse=True)

        return valid_builds[:5]  # Return top 5 builds

    async def _generate_llm_enhanced_builds(self, request, use_case_specs: Dict,
                                          component_data: Dict[str, List[ComponentData]]) -> List[BuildConfiguration]:
        """Generate builds using OpenRouter LLM intelligence"""
        builds = []

        try:
            # Prepare component data for LLM (limit to top options to avoid token limits)
            limited_component_data = {}
            for category, components in component_data.items():
                limited_component_data[category] = [
                    {
                        "name": comp.name,
                        "brand": comp.brand,
                        "price": comp.price,
                        "store": comp.store,
                        "is_special": comp.is_special,
                        "discount_percentage": comp.discount_percentage,
                        "specifications": comp.specifications,
                        "rating": comp.rating
                    }
                    for comp in components[:15]  # Top 15 per category
                ]

            # Get LLM recommendations
            llm_response = await self.openrouter_client.generate_build_recommendations(
                use_case=request.use_case,
                budget=request.budget,
                requirements=request.requirements,
                available_components=limited_component_data,
                model_tier="balanced"
            )

            if llm_response and "builds" in llm_response:
                for build_data in llm_response["builds"]:
                    # Convert LLM recommendation to BuildConfiguration
                    build_config = await self._convert_llm_build_to_config(
                        build_data, request, component_data
                    )
                    if build_config:
                        builds.append(build_config)

        except Exception as e:
            logger.error(f"Error in LLM build generation: {str(e)}")

        return builds

    async def _convert_llm_build_to_config(self, llm_build: Dict, request,
                                         component_data: Dict[str, List[ComponentData]]) -> Optional[BuildConfiguration]:
        """Convert LLM build recommendation to BuildConfiguration"""
        try:
            selected_components = {}

            # Find actual components matching LLM recommendations
            for category, component_name in llm_build.get("components", {}).items():
                if category in component_data:
                    # Find best matching component
                    matching_component = self._find_matching_component(
                        component_name, component_data[category]
                    )
                    if matching_component:
                        selected_components[category] = matching_component

            if len(selected_components) >= 4:  # Minimum viable build
                return await self._create_build_configuration(
                    selected_components, request, llm_build.get("name", "LLM Build")
                )

        except Exception as e:
            logger.error(f"Error converting LLM build: {str(e)}")

        return None

    def _find_matching_component(self, component_name: str,
                               available_components: List[ComponentData]) -> Optional[ComponentData]:
        """Find the best matching component from available options"""
        component_name_lower = component_name.lower()

        # Try exact name match first
        for component in available_components:
            if component.name.lower() == component_name_lower:
                return component

        # Try partial name match
        for component in available_components:
            if component_name_lower in component.name.lower():
                return component

        # Try brand + model match
        for component in available_components:
            component_text = f"{component.brand} {component.model}".lower()
            if component_name_lower in component_text:
                return component

        # Return first available component as fallback
        return available_components[0] if available_components else None

    async def _generate_balanced_build(self, request, budgets: Dict, components: Dict, specs: Dict) -> List[BuildConfiguration]:
        """Generate a balanced build focusing on overall performance"""
        builds = []

        try:
            # Select components for balanced build
            selected_components = {}

            # Start with GPU for gaming builds, CPU for others
            primary_component = "video_cards" if request.use_case == "gaming" else "cpu"

            # Select primary component first
            primary_budget = budgets.get(primary_component, request.budget * 0.3)
            selected_components[primary_component] = self._select_best_component(
                components.get(primary_component, []),
                primary_budget,
                "balanced"
            )

            if not selected_components[primary_component]:
                return builds

            # Adjust remaining budget
            remaining_budget = request.budget - selected_components[primary_component].price

            # Select other components in order of importance
            component_order = ["cpu", "video_cards", "ram", "motherboard", "storage", "psu", "case", "cooling"]

            for category in component_order:
                if category in selected_components:
                    continue

                if category not in components or not components[category]:
                    continue

                category_budget = min(budgets.get(category, remaining_budget * 0.2), remaining_budget * 0.5)

                component = self._select_compatible_component(
                    components[category],
                    selected_components,
                    category_budget,
                    "balanced"
                )

                if component:
                    selected_components[category] = component
                    remaining_budget -= component.price

            # Create build configuration
            if len(selected_components) >= 4:  # Minimum viable build
                build = await self._create_build_configuration(
                    selected_components, request, "Balanced Build"
                )
                builds.append(build)

        except Exception as e:
            logger.error(f"Error generating balanced build: {str(e)}")

        return builds

    async def _generate_performance_build(self, request, budgets: Dict, components: Dict, specs: Dict) -> List[BuildConfiguration]:
        """Generate a performance-focused build"""
        builds = []

        try:
            # Allocate more budget to performance components
            performance_allocation = budgets.copy()

            if request.use_case == "gaming":
                performance_allocation["video_cards"] *= 1.3
                performance_allocation["cpu"] *= 1.2
            else:
                performance_allocation["cpu"] *= 1.3
                performance_allocation["ram"] *= 1.2

            selected_components = {}
            remaining_budget = request.budget

            # Select high-performance components
            component_order = ["video_cards", "cpu", "ram", "motherboard", "storage", "psu", "case", "cooling"]

            for category in component_order:
                if category not in components or not components[category]:
                    continue

                category_budget = min(performance_allocation.get(category, remaining_budget * 0.2), remaining_budget * 0.6)

                component = self._select_compatible_component(
                    components[category],
                    selected_components,
                    category_budget,
                    "performance"
                )

                if component:
                    selected_components[category] = component
                    remaining_budget -= component.price

            if len(selected_components) >= 4:
                build = await self._create_build_configuration(
                    selected_components, request, "Performance Build"
                )
                builds.append(build)

        except Exception as e:
            logger.error(f"Error generating performance build: {str(e)}")

        return builds

    async def _generate_value_build(self, request, budgets: Dict, components: Dict, specs: Dict) -> List[BuildConfiguration]:
        """Generate a value-focused build prioritizing deals and efficiency"""
        builds = []

        try:
            selected_components = {}
            remaining_budget = request.budget

            # Prioritize components with good deals
            component_order = ["cpu", "video_cards", "ram", "motherboard", "storage", "psu", "case", "cooling"]

            for category in component_order:
                if category not in components or not components[category]:
                    continue

                # Look for deals first
                deal_components = [c for c in components[category] if c.is_special and c.price <= remaining_budget * 0.5]

                if deal_components:
                    component = self._select_compatible_component(
                        deal_components,
                        selected_components,
                        remaining_budget * 0.5,
                        "value"
                    )
                else:
                    category_budget = budgets.get(category, remaining_budget * 0.2)
                    component = self._select_compatible_component(
                        components[category],
                        selected_components,
                        category_budget,
                        "value"
                    )

                if component:
                    selected_components[category] = component
                    remaining_budget -= component.price

            if len(selected_components) >= 4:
                build = await self._create_build_configuration(
                    selected_components, request, "Value Build"
                )
                builds.append(build)

        except Exception as e:
            logger.error(f"Error generating value build: {str(e)}")

        return builds

    def _select_best_component(self, components: List[ComponentData], budget: float, strategy: str) -> Optional[ComponentData]:
        """Select the best component within budget for given strategy"""
        if not components:
            return None

        # Filter by budget
        affordable_components = [c for c in components if c.price <= budget]
        if not affordable_components:
            return None

        if strategy == "performance":
            # Sort by performance indicators (price as proxy, plus specs)
            return max(affordable_components, key=lambda x: self._calculate_performance_score(x))
        elif strategy == "value":
            # Sort by value score (performance per dollar)
            return max(affordable_components, key=lambda x: self._calculate_value_score_detailed(x))
        else:  # balanced
            # Balance between performance and value
            return max(affordable_components, key=lambda x:
                      (self._calculate_performance_score(x) + self._calculate_value_score_detailed(x)) / 2)

    def _select_compatible_component(self, components: List[ComponentData],
                                   existing_components: Dict[str, ComponentData],
                                   budget: float, strategy: str) -> Optional[ComponentData]:
        """Select a component that's compatible with existing components"""
        if not components:
            return None

        # Filter by budget and compatibility
        compatible_components = []

        for component in components:
            if component.price <= budget:
                if self.compatibility_checker.check_component_compatibility(component, existing_components):
                    compatible_components.append(component)

        if not compatible_components:
            return None

        return self._select_best_component(compatible_components, budget, strategy)

    def _calculate_performance_score(self, component: ComponentData) -> float:
        """Calculate performance score for a component"""
        score = 0.0

        # Base score from price (higher price often means better performance)
        score += min(component.price / 1000, 5.0)  # Normalize to 0-5 scale

        # Bonus from specifications
        if component.category == "video_cards":
            memory_gb = component.specifications.get("memory_gb", 0)
            score += memory_gb * 0.5
        elif component.category == "cpu":
            cores = component.specifications.get("cores", 0)
            clock_ghz = component.specifications.get("base_clock_ghz", 0)
            score += cores * 0.3 + clock_ghz * 0.5
        elif component.category == "ram":
            capacity_gb = component.specifications.get("capacity_gb", 0)
            speed_mhz = component.specifications.get("speed_mhz", 0)
            score += capacity_gb * 0.1 + speed_mhz / 1000

        # Rating bonus
        if component.rating:
            score += component.rating * 0.5

        return score

    def _calculate_value_score_detailed(self, component: ComponentData) -> float:
        """Calculate detailed value score (performance per dollar)"""
        performance_score = self._calculate_performance_score(component)

        if component.price <= 0:
            return 0.0

        value_score = performance_score / (component.price / 100)  # Performance per $100

        # Bonus for deals
        if component.is_special and component.discount_percentage:
            value_score *= (1 + component.discount_percentage / 100)

        return value_score

    async def _create_build_configuration(self, components: Dict[str, ComponentData],
                                        request, build_type: str) -> BuildConfiguration:
        """Create a build configuration from selected components"""
        total_cost = sum(component.price for component in components.values())
        stores_used = list(set(component.store for component in components.values()))

        # Calculate scores
        performance_score = self._calculate_build_performance_score(components, request.use_case)
        value_score = self._calculate_build_value_score(components, total_cost)
        compatibility_score = await self.compatibility_checker.check_build_compatibility(components)
        use_case_fit = self._calculate_use_case_fit(components, request.use_case)

        # Generate pros and cons
        pros, cons = self._generate_pros_cons(components, request, total_cost)

        # Generate build notes
        build_notes = self._generate_build_notes(components, request, build_type)

        return BuildConfiguration(
            components=components,
            total_cost=total_cost,
            performance_score=performance_score,
            value_score=value_score,
            compatibility_score=compatibility_score,
            use_case_fit=use_case_fit,
            stores_used=stores_used,
            pros=pros,
            cons=cons,
            build_notes=build_notes
        )

    def _calculate_build_performance_score(self, components: Dict[str, ComponentData], use_case: str) -> float:
        """Calculate overall build performance score"""
        weights = self.use_case_weights.get(use_case, self.use_case_weights["gaming"])

        total_score = 0.0
        total_weight = 0.0

        for category, component in components.items():
            if category in weights:
                component_score = self._calculate_performance_score(component)
                weight = weights[category]
                total_score += component_score * weight
                total_weight += weight

        return min(total_score / total_weight if total_weight > 0 else 0, 10.0)

    def _calculate_build_value_score(self, components: Dict[str, ComponentData], total_cost: float) -> float:
        """Calculate overall build value score"""
        if total_cost <= 0:
            return 0.0

        total_performance = sum(self._calculate_performance_score(comp) for comp in components.values())
        value_score = (total_performance / len(components)) / (total_cost / 1000)

        # Bonus for deals
        deal_bonus = sum(comp.discount_percentage or 0 for comp in components.values() if comp.is_special)
        value_score *= (1 + deal_bonus / 100)

        return min(value_score, 10.0)

    def _calculate_use_case_fit(self, components: Dict[str, ComponentData], use_case: str) -> float:
        """Calculate how well the build fits the use case"""
        # This is a simplified calculation
        # In a full implementation, this would consider specific requirements for each use case

        base_score = 7.0  # Base fit score

        if use_case == "gaming":
            if "video_cards" in components:
                gpu = components["video_cards"]
                memory_gb = gpu.specifications.get("memory_gb", 0)
                if memory_gb >= 8:
                    base_score += 1.5
                elif memory_gb >= 6:
                    base_score += 1.0

        elif use_case == "workstation":
            if "cpu" in components:
                cpu = components["cpu"]
                cores = cpu.specifications.get("cores", 0)
                if cores >= 12:
                    base_score += 1.5
                elif cores >= 8:
                    base_score += 1.0

        return min(base_score, 10.0)

    def _generate_pros_cons(self, components: Dict[str, ComponentData], request, total_cost: float) -> Tuple[List[str], List[str]]:
        """Generate pros and cons for the build"""
        pros = []
        cons = []

        # Budget analysis
        if total_cost <= request.budget * 0.9:
            pros.append("Under budget with room for upgrades")
        elif total_cost > request.budget:
            cons.append(f"Over budget by ${total_cost - request.budget:.0f}")

        # Deal analysis
        deal_count = sum(1 for comp in components.values() if comp.is_special)
        if deal_count >= 2:
            pros.append(f"Includes {deal_count} components on special")

        # Store diversity
        store_count = len(set(comp.store for comp in components.values()))
        if store_count == 1:
            pros.append("All components from single store (easier purchasing)")
        elif store_count <= 3:
            pros.append("Components from few stores (manageable purchasing)")
        else:
            cons.append("Components spread across many stores")

        # Component quality
        avg_rating = sum(comp.rating or 4.0 for comp in components.values()) / len(components)
        if avg_rating >= 4.5:
            pros.append("High-rated components")
        elif avg_rating < 3.5:
            cons.append("Some lower-rated components")

        return pros, cons

    def _generate_build_notes(self, components: Dict[str, ComponentData], request, build_type: str) -> str:
        """Generate detailed build notes"""
        notes = f"{build_type} optimized for {request.use_case}. "

        # Highlight key components
        if "video_cards" in components:
            gpu = components["video_cards"]
            notes += f"Features {gpu.brand} {gpu.model} graphics card. "

        if "cpu" in components:
            cpu = components["cpu"]
            notes += f"Powered by {cpu.brand} {cpu.model} processor. "

        # Mention deals
        deal_components = [comp for comp in components.values() if comp.is_special]
        if deal_components:
            notes += f"Includes special deals on {len(deal_components)} components. "

        return notes

    def _calculate_overall_score(self, build: BuildConfiguration, request) -> float:
        """Calculate overall score for ranking builds"""
        score = (
            build.performance_score * 0.3 +
            build.value_score * 0.3 +
            build.use_case_fit * 0.25 +
            build.compatibility_score * 0.15
        )

        # Budget penalty
        if build.total_cost > request.budget:
            over_budget_ratio = (build.total_cost - request.budget) / request.budget
            score *= (1 - min(over_budget_ratio, 0.5))

        return score
