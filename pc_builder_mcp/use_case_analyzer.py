#!/usr/bin/env python3
"""
Use Case Analyzer for PC Builder MCP

Analyzes different PC use cases and determines optimal specifications,
performance requirements, and component priorities.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class UseCaseSpecs:
    """Specifications and requirements for a specific use case"""
    use_case: str
    min_ram_gb: int
    recommended_ram_gb: int
    min_storage_gb: int
    recommended_storage_gb: int
    cpu_priority: float  # 0-1 scale
    gpu_priority: float  # 0-1 scale
    ram_priority: float  # 0-1 scale
    storage_priority: float  # 0-1 scale
    target_resolution: str
    target_fps: Optional[int]
    specific_requirements: Dict[str, Any]
    software_requirements: List[str]
    performance_notes: str

class UseCaseAnalyzer:
    """Analyzes PC use cases and determines optimal specifications"""
    
    def __init__(self):
        # Define use case specifications
        self.use_case_definitions = {
            "gaming": {
                "description": "Gaming PC for modern games at high settings",
                "min_ram_gb": 16,
                "recommended_ram_gb": 32,
                "min_storage_gb": 500,
                "recommended_storage_gb": 1000,
                "cpu_priority": 0.7,
                "gpu_priority": 1.0,
                "ram_priority": 0.6,
                "storage_priority": 0.5,
                "target_resolution": "1440p",
                "target_fps": 60,
                "specific_requirements": {
                    "gpu_memory_gb": 8,
                    "cpu_cores": 6,
                    "fast_storage": True,
                    "high_refresh_monitor": True
                },
                "software_requirements": [
                    "Steam", "Epic Games", "Modern AAA games", "Discord"
                ],
                "performance_notes": "Prioritize GPU performance for high frame rates and visual quality"
            },
            "workstation": {
                "description": "Professional workstation for CAD, 3D modeling, and engineering",
                "min_ram_gb": 32,
                "recommended_ram_gb": 64,
                "min_storage_gb": 1000,
                "recommended_storage_gb": 2000,
                "cpu_priority": 1.0,
                "gpu_priority": 0.8,
                "ram_priority": 1.0,
                "storage_priority": 0.8,
                "target_resolution": "4K",
                "target_fps": None,
                "specific_requirements": {
                    "cpu_cores": 12,
                    "ecc_memory": False,  # Preferred but not required
                    "professional_gpu": True,
                    "multiple_monitors": True,
                    "fast_storage": True
                },
                "software_requirements": [
                    "AutoCAD", "SolidWorks", "3ds Max", "Maya", "Blender"
                ],
                "performance_notes": "Balance CPU and GPU performance for professional applications"
            },
            "content_creation": {
                "description": "Content creation for video editing, streaming, and media production",
                "min_ram_gb": 32,
                "recommended_ram_gb": 64,
                "min_storage_gb": 1000,
                "recommended_storage_gb": 4000,
                "cpu_priority": 0.9,
                "gpu_priority": 0.8,
                "ram_priority": 0.9,
                "storage_priority": 1.0,
                "target_resolution": "4K",
                "target_fps": None,
                "specific_requirements": {
                    "cpu_cores": 8,
                    "gpu_memory_gb": 12,
                    "fast_storage": True,
                    "large_storage": True,
                    "color_accuracy": True
                },
                "software_requirements": [
                    "Adobe Premiere Pro", "After Effects", "DaVinci Resolve", 
                    "OBS Studio", "Photoshop"
                ],
                "performance_notes": "High CPU performance for encoding, GPU for acceleration, large fast storage"
            },
            "streaming": {
                "description": "Gaming and streaming setup",
                "min_ram_gb": 32,
                "recommended_ram_gb": 32,
                "min_storage_gb": 1000,
                "recommended_storage_gb": 2000,
                "cpu_priority": 0.9,
                "gpu_priority": 0.9,
                "ram_priority": 0.7,
                "storage_priority": 0.6,
                "target_resolution": "1440p",
                "target_fps": 144,
                "specific_requirements": {
                    "cpu_cores": 8,
                    "gpu_memory_gb": 8,
                    "encoding_capability": True,
                    "dual_pc_option": False
                },
                "software_requirements": [
                    "OBS Studio", "Streamlabs", "Discord", "Games", "Chat applications"
                ],
                "performance_notes": "Need headroom for both gaming and streaming simultaneously"
            },
            "ai_ml": {
                "description": "AI/ML development and training",
                "min_ram_gb": 32,
                "recommended_ram_gb": 128,
                "min_storage_gb": 1000,
                "recommended_storage_gb": 4000,
                "cpu_priority": 0.7,
                "gpu_priority": 1.0,
                "ram_priority": 1.0,
                "storage_priority": 0.8,
                "target_resolution": "1440p",
                "target_fps": None,
                "specific_requirements": {
                    "gpu_memory_gb": 24,
                    "cuda_cores": True,
                    "tensor_cores": True,
                    "nvlink_support": False,
                    "fast_storage": True
                },
                "software_requirements": [
                    "Python", "TensorFlow", "PyTorch", "CUDA", "Jupyter", "Docker"
                ],
                "performance_notes": "GPU memory and compute performance critical for model training"
            },
            "office": {
                "description": "General office work and productivity",
                "min_ram_gb": 8,
                "recommended_ram_gb": 16,
                "min_storage_gb": 256,
                "recommended_storage_gb": 512,
                "cpu_priority": 0.6,
                "gpu_priority": 0.2,
                "ram_priority": 0.5,
                "storage_priority": 0.4,
                "target_resolution": "1080p",
                "target_fps": None,
                "specific_requirements": {
                    "integrated_graphics": True,
                    "energy_efficient": True,
                    "quiet_operation": True,
                    "compact_size": True
                },
                "software_requirements": [
                    "Microsoft Office", "Web browsers", "Email clients", "PDF readers"
                ],
                "performance_notes": "Focus on efficiency and reliability over raw performance"
            },
            "general": {
                "description": "General purpose computing for mixed use",
                "min_ram_gb": 16,
                "recommended_ram_gb": 32,
                "min_storage_gb": 500,
                "recommended_storage_gb": 1000,
                "cpu_priority": 0.7,
                "gpu_priority": 0.6,
                "ram_priority": 0.6,
                "storage_priority": 0.5,
                "target_resolution": "1440p",
                "target_fps": None,
                "specific_requirements": {
                    "balanced_performance": True,
                    "upgrade_flexibility": True,
                    "good_value": True
                },
                "software_requirements": [
                    "Web browsers", "Office applications", "Media players", "Light gaming"
                ],
                "performance_notes": "Balanced build suitable for various tasks"
            }
        }
        
        # Budget-based adjustments
        self.budget_adjustments = {
            "low": {  # Under $1000
                "ram_reduction": 0.5,
                "storage_reduction": 0.5,
                "gpu_tier_reduction": 2,
                "cpu_tier_reduction": 1
            },
            "medium": {  # $1000-2500
                "ram_reduction": 0.8,
                "storage_reduction": 0.8,
                "gpu_tier_reduction": 1,
                "cpu_tier_reduction": 0
            },
            "high": {  # $2500-5000
                "ram_reduction": 1.0,
                "storage_reduction": 1.0,
                "gpu_tier_reduction": 0,
                "cpu_tier_reduction": 0
            },
            "enthusiast": {  # $5000+
                "ram_reduction": 1.2,
                "storage_reduction": 1.2,
                "gpu_tier_reduction": 0,
                "cpu_tier_reduction": 0
            }
        }
    
    def analyze_use_case(self, use_case: str, budget: float, 
                        user_requirements: Dict[str, Any]) -> UseCaseSpecs:
        """Analyze a use case and return optimized specifications"""
        
        if use_case not in self.use_case_definitions:
            logger.warning(f"Unknown use case: {use_case}, defaulting to 'general'")
            use_case = "general"
        
        base_specs = self.use_case_definitions[use_case].copy()
        
        # Determine budget tier
        budget_tier = self._determine_budget_tier(budget)
        
        # Apply budget adjustments
        adjusted_specs = self._apply_budget_adjustments(base_specs, budget_tier)
        
        # Apply user requirements
        final_specs = self._apply_user_requirements(adjusted_specs, user_requirements)
        
        # Create UseCaseSpecs object
        return UseCaseSpecs(
            use_case=use_case,
            min_ram_gb=final_specs["min_ram_gb"],
            recommended_ram_gb=final_specs["recommended_ram_gb"],
            min_storage_gb=final_specs["min_storage_gb"],
            recommended_storage_gb=final_specs["recommended_storage_gb"],
            cpu_priority=final_specs["cpu_priority"],
            gpu_priority=final_specs["gpu_priority"],
            ram_priority=final_specs["ram_priority"],
            storage_priority=final_specs["storage_priority"],
            target_resolution=final_specs["target_resolution"],
            target_fps=final_specs.get("target_fps"),
            specific_requirements=final_specs["specific_requirements"],
            software_requirements=final_specs["software_requirements"],
            performance_notes=final_specs["performance_notes"]
        )
    
    def _determine_budget_tier(self, budget: float) -> str:
        """Determine budget tier based on amount"""
        if budget < 1000:
            return "low"
        elif budget < 2500:
            return "medium"
        elif budget < 5000:
            return "high"
        else:
            return "enthusiast"
    
    def _apply_budget_adjustments(self, specs: Dict, budget_tier: str) -> Dict:
        """Apply budget-based adjustments to specifications"""
        adjusted = specs.copy()
        adjustments = self.budget_adjustments[budget_tier]
        
        # Adjust RAM requirements
        adjusted["min_ram_gb"] = max(8, int(adjusted["min_ram_gb"] * adjustments["ram_reduction"]))
        adjusted["recommended_ram_gb"] = max(16, int(adjusted["recommended_ram_gb"] * adjustments["ram_reduction"]))
        
        # Adjust storage requirements
        adjusted["min_storage_gb"] = max(256, int(adjusted["min_storage_gb"] * adjustments["storage_reduction"]))
        adjusted["recommended_storage_gb"] = max(512, int(adjusted["recommended_storage_gb"] * adjustments["storage_reduction"]))
        
        # Adjust target resolution for lower budgets
        if budget_tier == "low":
            if adjusted["target_resolution"] == "4K":
                adjusted["target_resolution"] = "1440p"
            elif adjusted["target_resolution"] == "1440p":
                adjusted["target_resolution"] = "1080p"
        
        # Adjust target FPS for lower budgets
        if adjusted.get("target_fps") and budget_tier == "low":
            adjusted["target_fps"] = max(30, adjusted["target_fps"] - 30)
        
        return adjusted
    
    def _apply_user_requirements(self, specs: Dict, user_requirements: Dict) -> Dict:
        """Apply user-specific requirements to specifications"""
        final = specs.copy()
        
        # Override with user-specified minimums
        if "min_ram" in user_requirements:
            final["min_ram_gb"] = max(final["min_ram_gb"], user_requirements["min_ram"])
        
        if "min_storage" in user_requirements:
            final["min_storage_gb"] = max(final["min_storage_gb"], user_requirements["min_storage"])
        
        if "target_resolution" in user_requirements:
            final["target_resolution"] = user_requirements["target_resolution"]
        
        if "target_fps" in user_requirements:
            final["target_fps"] = user_requirements["target_fps"]
        
        # Add specific software requirements
        if "specific_software" in user_requirements:
            final["software_requirements"].extend(user_requirements["specific_software"])
        
        return final
    
    def get_component_recommendations(self, specs: UseCaseSpecs, budget: float) -> Dict[str, Dict]:
        """Get component-specific recommendations based on use case specs"""
        recommendations = {}
        
        # CPU recommendations
        recommendations["cpu"] = self._get_cpu_recommendations(specs, budget)
        
        # GPU recommendations
        recommendations["gpu"] = self._get_gpu_recommendations(specs, budget)
        
        # RAM recommendations
        recommendations["ram"] = self._get_ram_recommendations(specs, budget)
        
        # Storage recommendations
        recommendations["storage"] = self._get_storage_recommendations(specs, budget)
        
        # Motherboard recommendations
        recommendations["motherboard"] = self._get_motherboard_recommendations(specs, budget)
        
        # PSU recommendations
        recommendations["psu"] = self._get_psu_recommendations(specs, budget)
        
        return recommendations
    
    def _get_cpu_recommendations(self, specs: UseCaseSpecs, budget: float) -> Dict:
        """Get CPU recommendations for the use case"""
        cpu_budget = budget * 0.25 * specs.cpu_priority
        
        recommendations = {
            "budget_allocation": cpu_budget,
            "priority": specs.cpu_priority,
            "min_cores": specs.specific_requirements.get("cpu_cores", 4),
            "preferred_features": [],
            "avoid_features": []
        }
        
        if specs.use_case == "gaming":
            recommendations["preferred_features"] = ["High single-core performance", "6+ cores", "Overclocking support"]
            recommendations["target_models"] = ["Ryzen 5 7600X", "Intel i5-13600K", "Ryzen 7 7700X"]
        
        elif specs.use_case == "workstation":
            recommendations["preferred_features"] = ["Many cores", "High multi-core performance", "ECC support"]
            recommendations["target_models"] = ["Ryzen 9 7950X", "Intel i9-13900K", "Threadripper"]
        
        elif specs.use_case == "content_creation":
            recommendations["preferred_features"] = ["8+ cores", "Good single and multi-core", "Hardware encoding"]
            recommendations["target_models"] = ["Ryzen 7 7700X", "Intel i7-13700K", "Ryzen 9 7900X"]
        
        elif specs.use_case == "ai_ml":
            recommendations["preferred_features"] = ["Many cores", "AVX support", "Large cache"]
            recommendations["target_models"] = ["Ryzen 9 7950X", "Intel i9-13900K", "Threadripper"]
        
        elif specs.use_case == "office":
            recommendations["preferred_features"] = ["Energy efficient", "Integrated graphics", "Low TDP"]
            recommendations["target_models"] = ["Ryzen 5 7600G", "Intel i5-13400", "Ryzen 3 7300G"]
        
        return recommendations
    
    def _get_gpu_recommendations(self, specs: UseCaseSpecs, budget: float) -> Dict:
        """Get GPU recommendations for the use case"""
        gpu_budget = budget * 0.35 * specs.gpu_priority
        
        recommendations = {
            "budget_allocation": gpu_budget,
            "priority": specs.gpu_priority,
            "min_memory_gb": specs.specific_requirements.get("gpu_memory_gb", 4),
            "preferred_features": [],
            "target_resolution": specs.target_resolution
        }
        
        if specs.use_case == "gaming":
            if specs.target_resolution == "4K":
                recommendations["target_models"] = ["RTX 4080", "RTX 4090", "RX 7900 XTX"]
            elif specs.target_resolution == "1440p":
                recommendations["target_models"] = ["RTX 4070", "RTX 4060 Ti", "RX 7800 XT"]
            else:  # 1080p
                recommendations["target_models"] = ["RTX 4060", "RX 7600", "RTX 3060"]
        
        elif specs.use_case == "workstation":
            recommendations["preferred_features"] = ["Professional drivers", "ECC memory", "Certified applications"]
            recommendations["target_models"] = ["RTX A4000", "RTX A5000", "RTX 4080"]
        
        elif specs.use_case == "content_creation":
            recommendations["preferred_features"] = ["Hardware encoding", "Large VRAM", "CUDA/OpenCL"]
            recommendations["target_models"] = ["RTX 4070", "RTX 4080", "RTX 4090"]
        
        elif specs.use_case == "ai_ml":
            recommendations["preferred_features"] = ["Tensor cores", "Large VRAM", "CUDA support"]
            recommendations["target_models"] = ["RTX 4090", "RTX A6000", "RTX 4080"]
        
        elif specs.use_case == "office":
            recommendations["preferred_features"] = ["Low power", "Basic display output"]
            recommendations["target_models"] = ["Integrated graphics", "GTX 1650", "Basic discrete"]
        
        return recommendations
    
    def _get_ram_recommendations(self, specs: UseCaseSpecs, budget: float) -> Dict:
        """Get RAM recommendations for the use case"""
        return {
            "min_capacity_gb": specs.min_ram_gb,
            "recommended_capacity_gb": specs.recommended_ram_gb,
            "priority": specs.ram_priority,
            "preferred_type": "DDR5" if budget > 1500 else "DDR4",
            "preferred_speed": "3200MHz+" if specs.use_case == "gaming" else "2666MHz+",
            "dual_channel": True,
            "ecc_preferred": specs.use_case == "workstation"
        }
    
    def _get_storage_recommendations(self, specs: UseCaseSpecs, budget: float) -> Dict:
        """Get storage recommendations for the use case"""
        return {
            "min_capacity_gb": specs.min_storage_gb,
            "recommended_capacity_gb": specs.recommended_storage_gb,
            "priority": specs.storage_priority,
            "primary_type": "NVMe SSD",
            "secondary_type": "SATA SSD or HDD" if specs.recommended_storage_gb > 1000 else None,
            "fast_storage_required": specs.specific_requirements.get("fast_storage", False),
            "large_storage_required": specs.specific_requirements.get("large_storage", False)
        }
    
    def _get_motherboard_recommendations(self, specs: UseCaseSpecs, budget: float) -> Dict:
        """Get motherboard recommendations for the use case"""
        return {
            "form_factor": "ATX" if budget > 1500 else "Micro-ATX",
            "chipset_tier": "High-end" if specs.use_case in ["workstation", "ai_ml"] else "Mid-range",
            "expansion_slots": "Multiple PCIe" if specs.use_case == "workstation" else "Standard",
            "memory_slots": 4 if specs.recommended_ram_gb >= 32 else 2,
            "overclocking_support": specs.use_case == "gaming"
        }
    
    def _get_psu_recommendations(self, specs: UseCaseSpecs, budget: float) -> Dict:
        """Get PSU recommendations for the use case"""
        base_wattage = 500
        
        if specs.use_case == "gaming":
            base_wattage = 650
        elif specs.use_case in ["workstation", "ai_ml", "content_creation"]:
            base_wattage = 750
        elif specs.use_case == "office":
            base_wattage = 450
        
        return {
            "min_wattage": base_wattage,
            "efficiency_rating": "80+ Gold" if budget > 1500 else "80+ Bronze",
            "modular": budget > 2000,
            "headroom_percentage": 20
        }
