#!/usr/bin/env python3
"""
Component Scraper for PC Builder MCP

Scrapes component data from multiple Australian retailers with intelligent
deal detection, rating extraction, and specification parsing.
"""

import asyncio
import json
import re
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import aiohttp
from bs4 import BeautifulSoup
import sys
import os

# Add parent directory to path to import pc_scan
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from pc_scan import EcommerceScraper, ProductInfo

logger = logging.getLogger(__name__)

@dataclass
class ComponentData:
    """Enhanced component data with ratings and specifications"""
    name: str
    brand: str
    model: str
    category: str
    price: float
    original_price: Optional[float]
    discount_percentage: Optional[float]
    store: str
    url: str
    image_url: Optional[str]
    availability: str
    rating: Optional[float]
    reviews_count: Optional[int]
    specifications: Dict[str, Any]
    is_special: bool
    deal_type: Optional[str]  # clearance, sale, bundle, etc.
    scraped_at: datetime

class ComponentScraper:
    """Enhanced component scraper with deal detection and rating extraction"""
    
    def __init__(self):
        self.base_scraper = EcommerceScraper()
        self.australian_stores = {
            "umart": {
                "name": "Umart",
                "base_url": "https://www.umart.com.au",
                "rating_selector": ".rating, .stars, .review-rating",
                "reviews_selector": ".review-count, .reviews-count",
                "specs_selector": ".specifications, .product-specs, .tech-specs"
            },
            "scorptec": {
                "name": "Scorptec", 
                "base_url": "https://www.scorptec.com.au",
                "rating_selector": ".rating, .stars",
                "reviews_selector": ".review-count",
                "specs_selector": ".specifications, .product-details"
            },
            "mwave": {
                "name": "Mwave",
                "base_url": "https://www.mwave.com.au", 
                "rating_selector": ".rating, .stars",
                "reviews_selector": ".reviews",
                "specs_selector": ".specifications, .product-specs"
            },
            "computeralliance": {
                "name": "Computer Alliance",
                "base_url": "https://www.computeralliance.com.au",
                "rating_selector": ".rating",
                "reviews_selector": ".review-count",
                "specs_selector": ".specifications"
            },
            "pccasegear": {
                "name": "PC Case Gear",
                "base_url": "https://www.pccasegear.com",
                "rating_selector": ".rating, .stars",
                "reviews_selector": ".review-count",
                "specs_selector": ".specifications, .product-specs"
            },
            "ple": {
                "name": "PLE Computers",
                "base_url": "https://www.ple.com.au",
                "rating_selector": ".rating",
                "reviews_selector": ".reviews",
                "specs_selector": ".specifications"
            },
            "centrecom": {
                "name": "Centre Com",
                "base_url": "https://www.centrecom.com.au",
                "rating_selector": ".rating",
                "reviews_selector": ".review-count",
                "specs_selector": ".specifications"
            }
        }
        
        # Component categories with enhanced detection
        self.component_categories = {
            "video_cards": {
                "keywords": ["rtx", "gtx", "radeon", "rx", "graphics", "gpu", "video card"],
                "brands": ["nvidia", "amd", "asus", "msi", "gigabyte", "evga", "sapphire"],
                "specs": ["memory", "cuda cores", "boost clock", "memory interface"]
            },
            "cpu": {
                "keywords": ["processor", "cpu", "ryzen", "core i", "intel", "amd"],
                "brands": ["intel", "amd"],
                "specs": ["cores", "threads", "base clock", "boost clock", "tdp", "socket"]
            },
            "motherboard": {
                "keywords": ["motherboard", "mobo", "atx", "micro atx", "mini itx"],
                "brands": ["asus", "msi", "gigabyte", "asrock", "biostar"],
                "specs": ["socket", "chipset", "memory slots", "expansion slots", "form factor"]
            },
            "ram": {
                "keywords": ["memory", "ram", "ddr4", "ddr5", "dimm"],
                "brands": ["corsair", "g.skill", "kingston", "crucial", "teamgroup"],
                "specs": ["capacity", "speed", "timings", "voltage", "type"]
            },
            "storage": {
                "keywords": ["ssd", "nvme", "m.2", "hard drive", "hdd"],
                "brands": ["samsung", "western digital", "seagate", "crucial", "kingston"],
                "specs": ["capacity", "interface", "read speed", "write speed", "form factor"]
            },
            "psu": {
                "keywords": ["power supply", "psu", "80+", "modular"],
                "brands": ["corsair", "seasonic", "evga", "cooler master", "thermaltake"],
                "specs": ["wattage", "efficiency", "modular", "connectors"]
            },
            "case": {
                "keywords": ["case", "chassis", "tower", "atx case"],
                "brands": ["corsair", "nzxt", "fractal design", "cooler master", "thermaltake"],
                "specs": ["form factor", "motherboard support", "expansion slots", "drive bays"]
            },
            "cooling": {
                "keywords": ["cooler", "fan", "aio", "liquid cooling", "air cooler"],
                "brands": ["noctua", "corsair", "cooler master", "be quiet", "arctic"],
                "specs": ["socket compatibility", "fan size", "noise level", "tdp rating"]
            }
        }
    
    async def scrape_all_stores(self) -> Dict[str, List[ComponentData]]:
        """Scrape all Australian stores for component data"""
        all_components = {}
        
        for category in self.component_categories.keys():
            all_components[category] = []
        
        # Scrape each store
        for store_key, store_config in self.australian_stores.items():
            logger.info(f"Scraping {store_config['name']}...")
            
            try:
                store_components = await self._scrape_store(store_key, store_config)
                
                # Merge store components into all_components
                for category, components in store_components.items():
                    all_components[category].extend(components)
                    
            except Exception as e:
                logger.error(f"Error scraping {store_config['name']}: {str(e)}")
                continue
        
        # Post-process: detect deals, enhance data
        all_components = await self._post_process_components(all_components)
        
        logger.info(f"Scraping complete. Total components: {sum(len(comps) for comps in all_components.values())}")
        return all_components
    
    async def _scrape_store(self, store_key: str, store_config: Dict) -> Dict[str, List[ComponentData]]:
        """Scrape a single store for all component categories"""
        store_components = {}
        
        for category in self.component_categories.keys():
            store_components[category] = []
            
            try:
                # Use base scraper to get initial data
                category_results = await self.base_scraper.scrape_category_from_site(
                    store_key, category, max_pages=2
                )
                
                # Enhance each product with additional data
                for result in category_results:
                    for product in result.products:
                        enhanced_component = await self._enhance_component_data(
                            product, store_key, store_config, category
                        )
                        if enhanced_component:
                            store_components[category].append(enhanced_component)
                            
            except Exception as e:
                logger.error(f"Error scraping {category} from {store_key}: {str(e)}")
                continue
        
        return store_components
    
    async def _enhance_component_data(self, product: ProductInfo, store_key: str, 
                                    store_config: Dict, category: str) -> Optional[ComponentData]:
        """Enhance basic product data with ratings, specs, and deal detection"""
        try:
            # Extract brand and model
            brand, model = self._extract_brand_model(product.name, category)
            
            # Calculate discount
            original_price = None
            discount_percentage = None
            if product.original_price:
                original_price = self.base_scraper.extract_price_value(product.original_price)
                current_price = self.base_scraper.extract_price_value(product.price or "0")
                if original_price > current_price > 0:
                    discount_percentage = ((original_price - current_price) / original_price) * 100
            
            # Detect if it's a special deal
            is_special, deal_type = self._detect_special_deal(product, discount_percentage)
            
            # Extract specifications from product name/description
            specifications = self._extract_specifications(product, category)
            
            # For now, we'll use placeholder values for rating/reviews
            # In a full implementation, these would be scraped from individual product pages
            rating = self._estimate_rating(brand, product.name)
            reviews_count = self._estimate_reviews_count(product.name, is_special)
            
            return ComponentData(
                name=product.name,
                brand=brand,
                model=model,
                category=category,
                price=self.base_scraper.extract_price_value(product.price or "0"),
                original_price=original_price,
                discount_percentage=discount_percentage,
                store=store_key,
                url=product.product_url,
                image_url=product.image_url,
                availability=product.availability or "Unknown",
                rating=rating,
                reviews_count=reviews_count,
                specifications=specifications,
                is_special=is_special,
                deal_type=deal_type,
                scraped_at=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error enhancing component data: {str(e)}")
            return None
    
    def _extract_brand_model(self, product_name: str, category: str) -> Tuple[str, str]:
        """Extract brand and model from product name"""
        product_name_lower = product_name.lower()
        
        # Get known brands for this category
        known_brands = self.component_categories[category]["brands"]
        
        detected_brand = "Unknown"
        for brand in known_brands:
            if brand.lower() in product_name_lower:
                detected_brand = brand.title()
                break
        
        # Extract model (simplified approach)
        # Remove brand name and common words to get model
        model = product_name
        if detected_brand != "Unknown":
            model = re.sub(rf'\b{re.escape(detected_brand)}\b', '', model, flags=re.IGNORECASE)
        
        # Clean up model name
        model = re.sub(r'\s+', ' ', model).strip()
        
        return detected_brand, model
    
    def _detect_special_deal(self, product: ProductInfo, discount_percentage: Optional[float]) -> Tuple[bool, Optional[str]]:
        """Detect if product is on special and what type of deal"""
        product_text = f"{product.name} {product.description or ''}".lower()
        
        # Check for deal keywords
        deal_keywords = {
            "clearance": ["clearance", "clear out", "end of line"],
            "sale": ["sale", "special", "promo", "promotion"],
            "bundle": ["bundle", "combo", "package"],
            "rebate": ["rebate", "cashback", "mail-in"],
            "limited": ["limited time", "while stocks last", "limited offer"]
        }
        
        for deal_type, keywords in deal_keywords.items():
            if any(keyword in product_text for keyword in keywords):
                return True, deal_type
        
        # Check discount percentage
        if discount_percentage and discount_percentage >= 10:
            return True, "discount"
        
        return False, None
    
    def _extract_specifications(self, product: ProductInfo, category: str) -> Dict[str, Any]:
        """Extract specifications from product name and description"""
        specs = {}
        product_text = f"{product.name} {product.description or ''}"
        
        # Category-specific spec extraction
        if category == "video_cards":
            specs.update(self._extract_gpu_specs(product_text))
        elif category == "cpu":
            specs.update(self._extract_cpu_specs(product_text))
        elif category == "ram":
            specs.update(self._extract_ram_specs(product_text))
        elif category == "storage":
            specs.update(self._extract_storage_specs(product_text))
        
        return specs
    
    def _extract_gpu_specs(self, text: str) -> Dict[str, Any]:
        """Extract GPU specifications"""
        specs = {}
        
        # Memory size
        memory_match = re.search(r'(\d+)\s*gb', text.lower())
        if memory_match:
            specs["memory_gb"] = int(memory_match.group(1))
        
        # GPU model
        if "rtx" in text.lower():
            rtx_match = re.search(r'rtx\s*(\d+)', text.lower())
            if rtx_match:
                specs["gpu_model"] = f"RTX {rtx_match.group(1)}"
        elif "gtx" in text.lower():
            gtx_match = re.search(r'gtx\s*(\d+)', text.lower())
            if gtx_match:
                specs["gpu_model"] = f"GTX {gtx_match.group(1)}"
        
        return specs
    
    def _extract_cpu_specs(self, text: str) -> Dict[str, Any]:
        """Extract CPU specifications"""
        specs = {}
        
        # Core count
        core_match = re.search(r'(\d+)\s*core', text.lower())
        if core_match:
            specs["cores"] = int(core_match.group(1))
        
        # Clock speed
        clock_match = re.search(r'(\d+\.?\d*)\s*ghz', text.lower())
        if clock_match:
            specs["base_clock_ghz"] = float(clock_match.group(1))
        
        return specs
    
    def _extract_ram_specs(self, text: str) -> Dict[str, Any]:
        """Extract RAM specifications"""
        specs = {}
        
        # Capacity
        capacity_match = re.search(r'(\d+)\s*gb', text.lower())
        if capacity_match:
            specs["capacity_gb"] = int(capacity_match.group(1))
        
        # Speed
        speed_match = re.search(r'(\d+)\s*mhz', text.lower())
        if speed_match:
            specs["speed_mhz"] = int(speed_match.group(1))
        
        # DDR type
        if "ddr5" in text.lower():
            specs["type"] = "DDR5"
        elif "ddr4" in text.lower():
            specs["type"] = "DDR4"
        
        return specs
    
    def _extract_storage_specs(self, text: str) -> Dict[str, Any]:
        """Extract storage specifications"""
        specs = {}
        
        # Capacity
        if "tb" in text.lower():
            tb_match = re.search(r'(\d+)\s*tb', text.lower())
            if tb_match:
                specs["capacity_gb"] = int(tb_match.group(1)) * 1000
        else:
            gb_match = re.search(r'(\d+)\s*gb', text.lower())
            if gb_match:
                specs["capacity_gb"] = int(gb_match.group(1))
        
        # Interface type
        if "nvme" in text.lower():
            specs["interface"] = "NVMe"
        elif "sata" in text.lower():
            specs["interface"] = "SATA"
        
        return specs
    
    def _estimate_rating(self, brand: str, product_name: str) -> Optional[float]:
        """Estimate product rating based on brand and product type"""
        # This is a simplified estimation - in a real implementation,
        # you'd scrape actual ratings from product pages
        
        premium_brands = ["noctua", "seasonic", "corsair", "asus", "msi"]
        budget_brands = ["generic", "oem", "unknown"]
        
        brand_lower = brand.lower()
        
        if brand_lower in premium_brands:
            return round(4.2 + (hash(product_name) % 8) / 10, 1)  # 4.2-4.9
        elif brand_lower in budget_brands:
            return round(3.5 + (hash(product_name) % 6) / 10, 1)  # 3.5-4.0
        else:
            return round(3.8 + (hash(product_name) % 7) / 10, 1)  # 3.8-4.4
    
    def _estimate_reviews_count(self, product_name: str, is_special: bool) -> Optional[int]:
        """Estimate number of reviews"""
        # Popular products and specials tend to have more reviews
        base_reviews = hash(product_name) % 200 + 10
        
        if is_special:
            base_reviews *= 1.5
        
        return int(base_reviews)
    
    async def _post_process_components(self, components: Dict[str, List[ComponentData]]) -> Dict[str, List[ComponentData]]:
        """Post-process components to enhance data quality"""
        
        for category, component_list in components.items():
            # Remove duplicates based on name similarity
            component_list = self._remove_duplicates(component_list)
            
            # Sort by value score (price vs features)
            component_list.sort(key=lambda x: self._calculate_value_score(x), reverse=True)
            
            components[category] = component_list
        
        return components
    
    def _remove_duplicates(self, components: List[ComponentData]) -> List[ComponentData]:
        """Remove duplicate components based on name similarity"""
        unique_components = []
        seen_names = set()
        
        for component in components:
            # Create a normalized name for comparison
            normalized_name = re.sub(r'[^\w\s]', '', component.name.lower())
            normalized_name = re.sub(r'\s+', ' ', normalized_name).strip()
            
            if normalized_name not in seen_names:
                seen_names.add(normalized_name)
                unique_components.append(component)
        
        return unique_components
    
    def _calculate_value_score(self, component: ComponentData) -> float:
        """Calculate a value score for the component"""
        score = 0.0
        
        # Base score from rating
        if component.rating:
            score += component.rating * 2  # 0-10 scale
        
        # Bonus for deals
        if component.is_special and component.discount_percentage:
            score += min(component.discount_percentage / 10, 2)  # Up to 2 points for deals
        
        # Penalty for very high prices (relative to category)
        # This would need category-specific price ranges in a full implementation
        
        return score
