# Australian PC Component Price Scraper

A comprehensive web scraper designed specifically for extracting PC component prices from major Australian hardware retailers. Built with crawl4ai for robust async scraping and optimized for MCP (Model Context Protocol) integration.

## 🎯 Target Components

The scraper focuses on the essential components needed for PC builds:

- **Video Cards** (Graphics Cards) - RTX, GTX, Radeon
- **ATX Motherboards** - Full ATX and Standard ATX
- **ATX Cases** - Mid Tower and Full Tower cases
- **RAM** (Memory) - DDR4/DDR5 Desktop Memory
- **CPU** (Processors) - Intel and AMD processors
- **Cooling** - CPU Coolers, Fans, AIO, Air Coolers
- **PSU** (Power Supplies) - Modular and Non-modular PSUs
- **M.2 Drives** - NVMe SSDs and M.2 Storage

## 🏪 Supported Australian Stores

### Major Retailers
- **Umart** - Leading Australian PC retailer
- **Scorptec** - Gaming PC specialists
- **Mwave** - Computer components and electronics
- **Computer Alliance** - Business and gaming solutions
- **PC Case Gear** - Premium gaming components
- **PLE Computers** - Western Australian focus
- **Centre Com** - Established PC hardware retailer

### Additional Stores
- Allied Gaming PC, Techfast, Buck PCs, Cobra Tech
- Radium PCs, PC Byte, Precision Computers, UMK Logix
- iGaming Computer, JW Computers, Cataclysm, Titanium PC
- BPC Tech, BYB Computers

### International
- iBUYPOWER (for comparison)

## 🚀 Quick Start

### Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Set up crawl4ai (if needed)
crawl4ai-setup
```

### Set API Key (Optional but Recommended)

```bash
export OPENAI_API_KEY="your-api-key-here"
```

### Basic Usage

```bash
# Scan single store
python pc_component_scraper_enhanced.py --store umart

# Scan multiple stores
python pc_component_scraper_enhanced.py --stores umart scorptec mwave

# Scan specific categories
python pc_component_scraper_enhanced.py --categories video_cards cpu ram

# Comprehensive scan (all stores)
python pc_component_scraper_enhanced.py --all --max-pages 1
```

## 📊 Output Format

The scraper generates MCP-optimized JSON files with the following structure:

```json
{
  "metadata": {
    "scraper": "Australian PC Component Scraper",
    "purpose": "PC Build Price Optimization",
    "timestamp": 1703001234,
    "categories": ["video_cards", "cpu", "ram", ...],
    "stores_scanned": ["umart", "scorptec", ...]
  },
  "components": {
    "video_cards": {
      "description": "Graphics Cards (RTX, GTX, Radeon)",
      "total_products": 150,
      "products": [
        {
          "name": "NVIDIA GeForce RTX 4070",
          "price": "$899.00",
          "price_numeric": 899.0,
          "store": "umart",
          "brand": "NVIDIA",
          "url": "https://...",
          "availability": "In Stock"
        }
      ],
      "price_range": {
        "min": 299.0,
        "max": 2499.0,
        "average": 756.5
      }
    }
  },
  "build_recommendations": {
    "budget_ranges": {
      "budget": {"max_price": 1500},
      "mid_range": {"max_price": 3000},
      "high_end": {"max_price": 5000},
      "enthusiast": {"max_price": 10000}
    }
  }
}
```

## 🔧 Advanced Usage

### Programmatic Usage

```python
import asyncio
from pc_component_scraper_enhanced import PCComponentManager

async def main():
    manager = PCComponentManager()
    
    # Scan specific store
    results = await manager.scan_single_store("umart", max_pages=2)
    
    # Export for MCP
    mcp_file = manager.export_for_mcp(results, "my_components.json")
    
    # Print summary
    manager.print_summary(results)

asyncio.run(main())
```

### Custom Category Selection

```python
# Focus on core gaming components
categories = ["video_cards", "cpu", "atx_motherboards", "ram"]

# Scan multiple stores for these categories
results = await manager.scan_multiple_stores(
    ["umart", "scorptec", "pccasegear"], 
    categories, 
    max_pages=2
)
```

## 📈 Example Use Cases

### 1. Budget Build Analysis
Find the best value components under specific price limits:

```bash
python australian_pc_scraper_examples.py
# Select option 3 for complete build pricing
```

### 2. Graphics Card Comparison
Compare GPU prices across all major stores:

```bash
python australian_pc_scraper_examples.py
# Select option 2 for graphics card comparison
```

### 3. Store Performance Analysis
Analyze which stores have the best selection and pricing:

```bash
python australian_pc_scraper_examples.py
# Select option 4 for store comparison
```

## 🔄 MCP Integration

The scraper is designed for seamless MCP integration:

1. **Structured Data**: All output follows consistent JSON schema
2. **Price Optimization**: Numeric price fields for easy comparison
3. **Build Recommendations**: Pre-categorized budget ranges
4. **Store Metadata**: Complete source information for verification

### Using with MCP

```python
# Load MCP-formatted data
with open("pc_components_for_mcp.json") as f:
    data = json.load(f)

# Access components by category
gpus = data["components"]["video_cards"]["products"]
cpus = data["components"]["cpu"]["products"]

# Get price ranges for budgeting
gpu_price_range = data["components"]["video_cards"]["price_range"]
```

## ⚙️ Configuration

### Store Configuration
Edit `australian_stores_config.json` to:
- Add new stores
- Update category URLs
- Modify price ranges
- Customize extraction parameters

### Component Categories
Modify categories in the scraper to focus on specific components:

```python
pc_components = {
    "video_cards": ["graphics card", "gpu", "rtx", "gtx"],
    "cpu": ["processor", "cpu", "intel", "amd", "ryzen"],
    # ... add more categories
}
```

## 🚨 Rate Limiting & Ethics

The scraper includes built-in rate limiting:
- 2-3 second delays between requests
- 3-5 second delays between stores
- Respectful crawling practices
- User-agent rotation

## 🔍 Troubleshooting

### Common Issues

1. **No data extracted**: Check if store URLs are still valid
2. **API key warnings**: Set OPENAI_API_KEY for better extraction
3. **Slow performance**: Reduce max_pages parameter
4. **Connection errors**: Check internet connection and store availability

### Debug Mode

```bash
python pc_component_scraper_enhanced.py --store umart --max-pages 1 --output debug.json
```

## 📝 Output Files

The scraper generates several types of files:

- `{store}_categorized_results_{timestamp}.json` - Individual store results
- `comprehensive_au_pc_scan_{timestamp}.json` - Multi-store comprehensive scan
- `pc_components_for_mcp_{timestamp}.json` - MCP-optimized format
- `gaming_pc_build.json` - Example build configurations

## 🎯 Future Enhancements

- Real-time price monitoring
- Price history tracking
- Advanced filtering and search
- Web dashboard for visualization
- Integration with PC build configurators

## 📄 License

This project is designed for educational and personal use. Please respect the terms of service of the websites being scraped.

---

**Ready to find the best PC component deals in Australia? Start scraping!** 🇦🇺💻
