#!/usr/bin/env python3
"""
Australian PC Component Scraper - Usage Examples

This script demonstrates various ways to use the enhanced scraper for Australian PC hardware stores.
"""

import asyncio
import json
from pathlib import Path
from pc_component_scraper_enhanced import PCComponentManager


async def example_single_store():
    """Example: Scan a single store for all PC components"""
    print("=== EXAMPLE 1: Single Store Scan ===")
    
    manager = PCComponentManager()
    
    # Scan Umart for all PC components
    results = await manager.scan_single_store("umart", max_pages=2)
    
    if results:
        print(f"Successfully scraped Umart!")
        manager.print_summary({"umart": results})
        
        # Export for MCP
        mcp_file = manager.export_for_mcp({"umart": results}, "umart_components.json")
        print(f"Data exported for MCP: {mcp_file}")
    else:
        print("No results from Umart scan.")


async def example_graphics_cards_comparison():
    """Example: Compare graphics card prices across stores"""
    print("\n=== EXAMPLE 2: Graphics Cards Price Comparison ===")
    
    manager = PCComponentManager()
    
    # Scan multiple stores for graphics cards only
    stores = ["umart", "scorptec", "mwave", "pccasegear"]
    categories = ["video_cards"]
    
    print(f"Comparing graphics card prices across {', '.join(stores)}")
    
    results = await manager.scan_multiple_stores(stores, categories, max_pages=2)
    
    if results:
        # Collect all graphics cards
        all_gpus = []
        
        for store, store_results in results.items():
            if "video_cards" in store_results:
                for result in store_results["video_cards"]:
                    for product in result.products:
                        price_numeric = manager.scraper.extract_price_value(product.price or "")
                        if price_numeric > 0:
                            all_gpus.append({
                                "name": product.name,
                                "price": price_numeric,
                                "store": store,
                                "url": product.product_url,
                                "brand": product.brand or "Unknown"
                            })
        
        if all_gpus:
            # Sort by price
            all_gpus.sort(key=lambda x: x["price"])
            
            print(f"\nFound {len(all_gpus)} graphics cards across all stores:")
            print("\nTop 10 Best Value Graphics Cards:")
            
            for i, gpu in enumerate(all_gpus[:10], 1):
                print(f"{i:2d}. {gpu['name'][:50]:<50} ${gpu['price']:>8.2f} ({gpu['store']})")
            
            # Show premium options
            print(f"\nTop 5 Premium Graphics Cards:")
            for i, gpu in enumerate(all_gpus[-5:], 1):
                print(f"{i:2d}. {gpu['name'][:50]:<50} ${gpu['price']:>8.2f} ({gpu['store']})")


async def example_complete_build_pricing():
    """Example: Get pricing for a complete PC build"""
    print("\n=== EXAMPLE 3: Complete PC Build Pricing ===")
    
    manager = PCComponentManager()
    
    # Scan stores known for good pricing
    stores = ["umart", "centrecom", "techfast"]
    
    results = await manager.scan_multiple_stores(stores, max_pages=1)
    
    if results:
        # Define build requirements
        build_components = {
            "video_cards": {"min_price": 300, "max_price": 800, "target": "RTX 4060"},
            "cpu": {"min_price": 200, "max_price": 500, "target": "Ryzen 5 or Intel i5"},
            "atx_motherboards": {"min_price": 100, "max_price": 250, "target": "B550 or B660"},
            "ram": {"min_price": 80, "max_price": 200, "target": "16GB DDR4/DDR5"},
            "atx_cases": {"min_price": 60, "max_price": 150, "target": "Mid Tower ATX"},
            "psu": {"min_price": 80, "max_price": 180, "target": "650W 80+ Gold"},
            "cooling": {"min_price": 30, "max_price": 100, "target": "Air Cooler"},
            "m2_drives": {"min_price": 60, "max_price": 150, "target": "1TB NVMe"}
        }
        
        print("Gaming PC Build Components (Mid-Range):")
        print("=" * 80)
        
        build_total = 0
        build_items = {}
        
        for category, requirements in build_components.items():
            category_options = []
            
            for store, store_results in results.items():
                if category in store_results:
                    for result in store_results[category]:
                        for product in result.products:
                            price_numeric = manager.scraper.extract_price_value(product.price or "")
                            
                            if requirements["min_price"] <= price_numeric <= requirements["max_price"]:
                                category_options.append({
                                    "name": product.name,
                                    "price": price_numeric,
                                    "store": store,
                                    "url": product.product_url
                                })
            
            if category_options:
                # Sort by price and pick best value (around 40th percentile)
                category_options.sort(key=lambda x: x["price"])
                best_value_index = min(len(category_options) - 1, int(len(category_options) * 0.4))
                selected = category_options[best_value_index]
                
                build_items[category] = selected
                build_total += selected["price"]
                
                print(f"{category.replace('_', ' ').title():<20} ${selected['price']:>8.2f} - {selected['name'][:40]} ({selected['store']})")
            else:
                print(f"{category.replace('_', ' ').title():<20} {'No options found in price range'}")
        
        print("=" * 80)
        print(f"{'TOTAL BUILD COST':<20} ${build_total:>8.2f}")
        print("=" * 80)
        
        # Export build details
        build_data = {
            "build_type": "Mid-Range Gaming PC",
            "total_cost": build_total,
            "components": build_items,
            "stores_used": list(set(item["store"] for item in build_items.values())),
            "timestamp": asyncio.get_event_loop().time()
        }
        
        with open("gaming_pc_build.json", "w") as f:
            json.dump(build_data, f, indent=2)
        
        print(f"Build details saved to: gaming_pc_build.json")


async def example_store_comparison():
    """Example: Compare stores for specific component categories"""
    print("\n=== EXAMPLE 4: Store Comparison Analysis ===")
    
    manager = PCComponentManager()
    
    # Compare top Australian stores
    stores = ["umart", "scorptec", "mwave", "pccasegear", "centrecom"]
    categories = ["video_cards", "cpu", "ram"]  # Focus on key components
    
    results = await manager.scan_multiple_stores(stores, categories, max_pages=1)
    
    if results:
        print("Store Comparison for Key Components:")
        print("=" * 60)
        
        store_stats = {}
        
        for store, store_results in results.items():
            store_stats[store] = {
                "total_products": 0,
                "categories": {},
                "avg_prices": {}
            }
            
            for category, category_results in store_results.items():
                products = []
                for result in category_results:
                    products.extend(result.products)
                
                prices = [manager.scraper.extract_price_value(p.price or "") for p in products]
                valid_prices = [p for p in prices if p > 0]
                
                store_stats[store]["categories"][category] = len(products)
                store_stats[store]["total_products"] += len(products)
                
                if valid_prices:
                    store_stats[store]["avg_prices"][category] = sum(valid_prices) / len(valid_prices)
        
        # Print comparison
        for store, stats in store_stats.items():
            print(f"\n{store.upper()}:")
            print(f"  Total products: {stats['total_products']}")
            
            for category in categories:
                product_count = stats["categories"].get(category, 0)
                avg_price = stats["avg_prices"].get(category, 0)
                
                if product_count > 0:
                    print(f"  {category.replace('_', ' ').title()}: {product_count} products, avg ${avg_price:.2f}")
                else:
                    print(f"  {category.replace('_', ' ').title()}: No products found")


async def main():
    """Run selected examples"""
    print("Australian PC Component Scraper - Usage Examples")
    print("=" * 60)
    
    print("Available examples:")
    print("1. Single store scan (Umart)")
    print("2. Graphics cards price comparison")
    print("3. Complete PC build pricing")
    print("4. Store comparison analysis")
    print("5. Run all examples")
    
    choice = input("\nSelect example to run (1-5): ").strip()
    
    if choice == "1":
        await example_single_store()
    elif choice == "2":
        await example_graphics_cards_comparison()
    elif choice == "3":
        await example_complete_build_pricing()
    elif choice == "4":
        await example_store_comparison()
    elif choice == "5":
        await example_single_store()
        await example_graphics_cards_comparison()
        await example_complete_build_pricing()
        await example_store_comparison()
    else:
        print("Invalid choice. Running single store example...")
        await example_single_store()
    
    print("\n" + "="*60)
    print("Examples completed!")
    print("Check the generated JSON files for MCP integration.")


if __name__ == "__main__":
    # Check for API key
    import os
    if not os.getenv('OPENAI_API_KEY'):
        print("Warning: OPENAI_API_KEY not set. Extraction quality may be reduced.")
        print("Set with: export OPENAI_API_KEY='your-api-key-here'")
        print()
    
    asyncio.run(main())
